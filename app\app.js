// Enhanced ARIMA Forecasting Dashboard JavaScript

// Global variables
let apiEndpoint = 'http://localhost:8001';
let currentChart = null;
let refreshInterval = null;
let isTraining = false;

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    checkApiStatus();
    loadSettings();
    startAutoRefresh();
});

// Initialize application
function initializeApp() {
    console.log('🚀 Enhanced ARIMA Dashboard initialized');
    showTab('dashboard');
    refreshSystemStatus();
}

// Tab Management
function showTab(tabName) {
    // Hide all tab contents
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(tab => tab.classList.remove('active'));

    // Remove active class from all nav tabs
    const navTabs = document.querySelectorAll('.nav-tab');
    navTabs.forEach(tab => tab.classList.remove('active'));

    // Show selected tab content
    const selectedTab = document.getElementById(tabName);
    if (selectedTab) {
        selectedTab.classList.add('active');
    }

    // Add active class to selected nav tab
    const selectedNavTab = document.querySelector(`[onclick="showTab('${tabName}')"]`);
    if (selectedNavTab) {
        selectedNavTab.classList.add('active');
    }

    // Load tab-specific content
    switch(tabName) {
        case 'dashboard':
            refreshSystemStatus();
            break;
        case 'models':
            refreshModelStatus();
            break;
        case 'forecasting':
            initializeForecastChart();
            break;
        case 'analytics':
            loadAnalytics();
            break;
        case 'settings':
            loadSettings();
            break;
    }
}

// API Status Check
async function checkApiStatus() {
    try {
        const response = await fetch(`${apiEndpoint}/`);
        const data = await response.json();

        updateApiStatus(true, 'Connected');
        console.log('✅ API connected:', data.message);

    } catch (error) {
        updateApiStatus(false, 'Disconnected');
        console.error('❌ API connection failed:', error);
    }
}

function updateApiStatus(connected, message) {
    const statusElement = document.getElementById('apiStatus');
    const statusElements = document.querySelectorAll('#apiServerStatus, #settingsApiStatus');

    if (statusElement) {
        statusElement.className = `status-indicator ${connected ? 'connected' : 'disconnected'}`;
        statusElement.innerHTML = `<i class="fas fa-circle"></i><span>${message}</span>`;
    }

    statusElements.forEach(element => {
        if (element) {
            element.innerHTML = connected ?
                '<i class="fas fa-check-circle" style="color: #28a745;"></i> Connected' :
                '<i class="fas fa-times-circle" style="color: #dc3545;"></i> Disconnected';
        }
    });
}

// System Status Management
async function refreshSystemStatus() {
    try {
        showLoading('Refreshing system status...');

        // Check API status
        await checkApiStatus();

        // Get model status
        const modelResponse = await fetch(`${apiEndpoint}/model-status`);
        const modelData = await modelResponse.json();

        updateSystemStatus(modelData);

    } catch (error) {
        console.error('Error refreshing system status:', error);
        showNotification('Failed to refresh system status', 'error');
    } finally {
        hideLoading();
    }
}

function updateSystemStatus(modelData) {
    // Update models count
    const modelsCountElement = document.getElementById('modelsCount');
    if (modelsCountElement) {
        const count = modelData.models_count || 0;
        modelsCountElement.innerHTML = `<i class="fas fa-brain"></i> ${count} Models`;
    }

    // Update ensemble status
    const ensembleStatusElement = document.getElementById('ensembleStatus');
    if (ensembleStatusElement) {
        const enabled = modelData.ensemble_enabled || false;
        ensembleStatusElement.innerHTML = enabled ?
            '<i class="fas fa-check-circle" style="color: #28a745;"></i> Enabled' :
            '<i class="fas fa-times-circle" style="color: #dc3545;"></i> Disabled';
    }

    // Update last training
    const lastTrainingElement = document.getElementById('lastTraining');
    if (lastTrainingElement) {
        const status = modelData.status;
        if (status === 'ready') {
            lastTrainingElement.innerHTML = '<i class="fas fa-clock"></i> Recently';
        } else {
            lastTrainingElement.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Not trained';
        }
    }

    // Update performance metrics
    updatePerformanceMetrics(modelData.performance_metrics || {});
}

function updatePerformanceMetrics(metrics) {
    const performanceElement = document.getElementById('performanceMetrics');
    if (!performanceElement) return;

    const accuracy = metrics.accuracy ? `${(metrics.accuracy * 100).toFixed(1)}%` : '--';
    const mape = metrics.mape ? `${metrics.mape.toFixed(2)}%` : '--';
    const rmse = metrics.rmse ? metrics.rmse.toFixed(2) : '--';

    performanceElement.innerHTML = `
        <div class="metric-item">
            <div class="metric-label">Accuracy</div>
            <div class="metric-value">${accuracy}</div>
        </div>
        <div class="metric-item">
            <div class="metric-label">MAPE</div>
            <div class="metric-value">${mape}</div>
        </div>
        <div class="metric-item">
            <div class="metric-label">RMSE</div>
            <div class="metric-value">${rmse}</div>
        </div>
    `;
}

// Model Management
async function trainModels() {
    if (isTraining) {
        showNotification('Training is already in progress', 'warning');
        return;
    }

    try {
        isTraining = true;
        showLoading('Training enhanced ARIMA models...');
        updateTrainingUI(true);

        const response = await fetch(`${apiEndpoint}/train`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();

        if (response.ok) {
            showNotification(`Training completed! ${data.models_trained} models trained.`, 'success');
            updateTrainingResults(data);
            refreshSystemStatus();
        } else {
            throw new Error(data.detail || 'Training failed');
        }

    } catch (error) {
        console.error('Training error:', error);
        showNotification(`Training failed: ${error.message}`, 'error');
    } finally {
        isTraining = false;
        hideLoading();
        updateTrainingUI(false);
    }
}

function updateTrainingUI(training) {
    const trainBtn = document.getElementById('trainBtn');
    const stopBtn = document.getElementById('stopBtn');
    const progressElement = document.getElementById('trainingProgress');

    if (trainBtn) {
        trainBtn.disabled = training;
        trainBtn.innerHTML = training ?
            '<i class="fas fa-spinner fa-spin"></i> Training...' :
            '<i class="fas fa-play"></i> Start Training';
    }

    if (stopBtn) {
        stopBtn.disabled = !training;
    }

    if (progressElement) {
        progressElement.style.display = training ? 'block' : 'none';
        if (training) {
            simulateProgress();
        }
    }
}

function simulateProgress() {
    const progressFill = document.getElementById('progressFill');
    const progressText = document.getElementById('progressText');

    if (!progressFill || !progressText) return;

    let progress = 0;
    const steps = [
        'Initializing models...',
        'Loading data...',
        'Optimizing parameters...',
        'Training ensemble...',
        'Validating models...',
        'Finalizing...'
    ];

    const interval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress > 100) progress = 100;

        progressFill.style.width = `${progress}%`;

        const stepIndex = Math.floor((progress / 100) * steps.length);
        if (stepIndex < steps.length) {
            progressText.textContent = steps[stepIndex];
        }

        if (progress >= 100 || !isTraining) {
            clearInterval(interval);
            progressText.textContent = 'Training completed!';
        }
    }, 500);
}

function updateTrainingResults(data) {
    const resultsElement = document.getElementById('trainingResults');
    const resultsGrid = document.getElementById('resultsGrid');

    if (!resultsElement || !resultsGrid) return;

    resultsGrid.innerHTML = `
        <div class="result-item">
            <div class="result-label">Models Trained</div>
            <div class="result-value">${data.models_trained}</div>
        </div>
        <div class="result-item">
            <div class="result-label">Training Time</div>
            <div class="result-value">${data.performance_metrics?.training_time?.toFixed(2) || '--'}s</div>
        </div>
        <div class="result-item">
            <div class="result-label">Best AIC</div>
            <div class="result-value">${data.validation_results?.best_aic?.toFixed(2) || '--'}</div>
        </div>
        <div class="result-item">
            <div class="result-label">Ensemble Accuracy</div>
            <div class="result-value">${data.validation_results?.ensemble_accuracy ? (data.validation_results.ensemble_accuracy * 100).toFixed(1) + '%' : '--'}</div>
        </div>
    `;

    resultsElement.style.display = 'block';
}

async function refreshModelStatus() {
    try {
        const response = await fetch(`${apiEndpoint}/model-status`);
        const data = await response.json();

        updateModelList(data);

    } catch (error) {
        console.error('Error refreshing model status:', error);
        showNotification('Failed to refresh model status', 'error');
    }
}

function updateModelList(data) {
    const modelListElement = document.getElementById('modelList');
    if (!modelListElement) return;

    if (data.status === 'not_trained') {
        modelListElement.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-brain"></i>
                <p>No models trained yet</p>
                <button class="btn-link" onclick="trainModels()">Train Models</button>
            </div>
        `;
        return;
    }

    const models = data.model_details || {};
    let modelsHtml = '';

    Object.keys(models).forEach(modelId => {
        const model = models[modelId];
        modelsHtml += `
            <div class="model-item">
                <div class="model-name">${modelId}</div>
                <div class="model-status">Status: Ready</div>
                <div class="model-metrics">
                    <span class="model-metric">AIC: ${model.aic?.toFixed(2) || '--'}</span>
                    <span class="model-metric">Parameters: ${model.order ? model.order.join(',') : '--'}</span>
                </div>
            </div>
        `;
    });

    modelListElement.innerHTML = modelsHtml || `
        <div class="empty-state">
            <i class="fas fa-brain"></i>
            <p>No model details available</p>
        </div>
    `;
}

// Forecasting Functions
async function generateForecast() {
    try {
        showLoading('Generating forecast...');

        const horizon = document.getElementById('forecastHorizon').value;
        const confidence = document.getElementById('confidenceLevel').value;
        const type = document.getElementById('forecastType').value;
        const sampleData = document.getElementById('sampleData').value;

        const requestBody = {
            steps: parseInt(horizon),
            confidence_level: parseFloat(confidence),
            metric: type
        };

        // Add sample data if selected
        if (sampleData) {
            requestBody.sample_part = parseInt(sampleData);
        }

        const response = await fetch(`${apiEndpoint}/forecast`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });

        const data = await response.json();

        if (response.ok) {
            displayForecastResults(data);
            showNotification('Forecast generated successfully!', 'success');
        } else {
            throw new Error(data.detail || 'Forecast generation failed');
        }

    } catch (error) {
        console.error('Forecast error:', error);
        showNotification(`Forecast failed: ${error.message}`, 'error');
    } finally {
        hideLoading();
    }
}

function displayForecastResults(data) {
    // Update forecast chart
    updateForecastChart(data);

    // Update forecast summary
    updateForecastSummary(data);

    // Show forecast summary
    const summaryElement = document.getElementById('forecastSummary');
    if (summaryElement) {
        summaryElement.style.display = 'block';
    }
}

function updateForecastChart(data) {
    const ctx = document.getElementById('forecastChart');
    if (!ctx) return;

    // Destroy existing chart
    if (currentChart) {
        currentChart.destroy();
    }

    const dates = data.dates || [];
    const forecast = data.forecast || [];
    const confidenceIntervals = data.confidence_intervals || [];

    // Prepare confidence interval data
    const upperBound = confidenceIntervals.map(ci => ci[1]);
    const lowerBound = confidenceIntervals.map(ci => ci[0]);

    currentChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: dates,
            datasets: [
                {
                    label: 'Forecast',
                    data: forecast,
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    borderWidth: 3,
                    fill: false,
                    tension: 0.4
                },
                {
                    label: 'Upper Confidence',
                    data: upperBound,
                    borderColor: 'rgba(102, 126, 234, 0.3)',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    borderWidth: 1,
                    fill: '+1',
                    tension: 0.4
                },
                {
                    label: 'Lower Confidence',
                    data: lowerBound,
                    borderColor: 'rgba(102, 126, 234, 0.3)',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    borderWidth: 1,
                    fill: false,
                    tension: 0.4
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'Enhanced ARIMA Forecast',
                    font: {
                        size: 16,
                        weight: 'bold'
                    }
                },
                legend: {
                    display: true,
                    position: 'top'
                }
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: 'Date'
                    }
                },
                y: {
                    display: true,
                    title: {
                        display: true,
                        text: 'Value'
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    });
}

function updateForecastSummary(data) {
    const forecast = data.forecast || [];
    const confidence = data.confidence_intervals || [];
    const modelInfo = data.model_info || {};

    // Calculate summary statistics
    const avgPrediction = forecast.length > 0 ?
        (forecast.reduce((a, b) => a + b, 0) / forecast.length).toFixed(2) : '--';

    const confidenceRange = confidence.length > 0 ?
        `±${((confidence[0][1] - confidence[0][0]) / 2).toFixed(2)}` : '--';

    const accuracy = modelInfo.accuracy ?
        `${(modelInfo.accuracy * 100).toFixed(1)}%` : '--';

    // Update summary elements
    const elements = {
        'forecastPeriodValue': `${forecast.length} periods`,
        'avgPredictionValue': avgPrediction,
        'confidenceRangeValue': confidenceRange,
        'modelAccuracyValue': accuracy
    };

    Object.keys(elements).forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = elements[id];
        }
    });
}

function clearForecast() {
    // Clear chart
    if (currentChart) {
        currentChart.destroy();
        currentChart = null;
    }

    // Hide forecast summary
    const summaryElement = document.getElementById('forecastSummary');
    if (summaryElement) {
        summaryElement.style.display = 'none';
    }

    // Initialize empty chart
    initializeForecastChart();
}

function initializeForecastChart() {
    const ctx = document.getElementById('forecastChart');
    if (!ctx) return;

    if (currentChart) {
        currentChart.destroy();
    }

    currentChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: []
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'Generate a forecast to see results',
                    font: {
                        size: 16
                    }
                }
            }
        }
    });
}

// Quick Actions
async function quickForecast() {
    try {
        showLoading('Generating quick forecast...');

        const response = await fetch(`${apiEndpoint}/forecast/tomorrow`);
        const data = await response.json();

        if (response.ok) {
            showNotification(`Tomorrow's forecast: ${data.forecast?.toFixed(2) || 'N/A'}`, 'info');
        } else {
            throw new Error(data.detail || 'Quick forecast failed');
        }

    } catch (error) {
        console.error('Quick forecast error:', error);
        showNotification(`Quick forecast failed: ${error.message}`, 'error');
    } finally {
        hideLoading();
    }
}

async function viewPerformance() {
    try {
        showLoading('Loading performance metrics...');

        const response = await fetch(`${apiEndpoint}/performance`);
        const data = await response.json();

        if (response.ok) {
            showTab('analytics');
            updateAnalyticsCharts(data);
            showNotification('Performance metrics loaded', 'success');
        } else {
            throw new Error(data.detail || 'Failed to load performance metrics');
        }

    } catch (error) {
        console.error('Performance error:', error);
        showNotification(`Failed to load performance: ${error.message}`, 'error');
    } finally {
        hideLoading();
    }
}

// Analytics Functions
function showAnalytics(type) {
    // Hide all analytics content
    const analyticsContents = document.querySelectorAll('.analytics-content');
    analyticsContents.forEach(content => content.classList.remove('active'));

    // Remove active class from all analytics tabs
    const analyticsTabs = document.querySelectorAll('.analytics-tab');
    analyticsTabs.forEach(tab => tab.classList.remove('active'));

    // Show selected analytics content
    const selectedContent = document.getElementById(`${type}-analytics`);
    if (selectedContent) {
        selectedContent.classList.add('active');
    }

    // Add active class to selected analytics tab
    const selectedTab = document.querySelector(`[onclick="showAnalytics('${type}')"]`);
    if (selectedTab) {
        selectedTab.classList.add('active');
    }

    // Load analytics data for the selected type
    loadAnalyticsData(type);
}

function loadAnalytics() {
    loadAnalyticsData('accuracy');
}

function loadAnalyticsData(type) {
    // This would typically load real analytics data
    // For now, we'll create sample data
    const sampleData = generateSampleAnalyticsData(type);
    updateAnalyticsChart(type, sampleData);
}

function generateSampleAnalyticsData(type) {
    switch(type) {
        case 'accuracy':
            return {
                labels: ['Model 1', 'Model 2', 'Model 3', 'Model 4', 'Ensemble'],
                data: [75, 78, 82, 79, 87],
                backgroundColor: ['#ff6384', '#36a2eb', '#cc65fe', '#ffce56', '#667eea']
            };
        case 'speed':
            return {
                labels: ['Parameter Search', 'Training', 'Prediction', 'Validation'],
                data: [2.5, 15.3, 0.8, 3.2],
                backgroundColor: ['#667eea', '#764ba2', '#f093fb', '#f5576c']
            };
        case 'comparison':
            return {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                datasets: [
                    {
                        label: 'Standard ARIMA',
                        data: [65, 68, 70, 72, 71, 73],
                        borderColor: '#ff6384',
                        backgroundColor: 'rgba(255, 99, 132, 0.1)'
                    },
                    {
                        label: 'Enhanced ARIMA',
                        data: [78, 82, 85, 87, 86, 89],
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)'
                    }
                ]
            };
        default:
            return { labels: [], data: [] };
    }
}

function updateAnalyticsChart(type, data) {
    const ctx = document.getElementById(`${type}Chart`);
    if (!ctx) return;

    // Destroy existing chart
    if (ctx.chart) {
        ctx.chart.destroy();
    }

    let chartConfig;

    if (type === 'comparison') {
        chartConfig = {
            type: 'line',
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Model Comparison Over Time'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Accuracy (%)'
                        }
                    }
                }
            }
        };
    } else {
        chartConfig = {
            type: type === 'speed' ? 'bar' : 'doughnut',
            data: {
                labels: data.labels,
                datasets: [{
                    data: data.data,
                    backgroundColor: data.backgroundColor
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: type === 'accuracy' ? 'Model Accuracy Comparison' : 'Training Speed (seconds)'
                    }
                }
            }
        };
    }

    ctx.chart = new Chart(ctx, chartConfig);
}

function updateAnalyticsCharts(data) {
    // Update all analytics charts with real data
    updateAnalyticsChart('accuracy', data.accuracy || generateSampleAnalyticsData('accuracy'));
    updateAnalyticsChart('speed', data.speed || generateSampleAnalyticsData('speed'));
    updateAnalyticsChart('comparison', data.comparison || generateSampleAnalyticsData('comparison'));
}

// Settings Management
function loadSettings() {
    const savedEndpoint = localStorage.getItem('apiEndpoint');
    const savedTimeout = localStorage.getItem('requestTimeout');
    const savedRefreshInterval = localStorage.getItem('refreshInterval');
    const savedTheme = localStorage.getItem('chartTheme');

    if (savedEndpoint) {
        apiEndpoint = savedEndpoint;
        const endpointInput = document.getElementById('apiEndpoint');
        if (endpointInput) endpointInput.value = savedEndpoint;
    }

    if (savedTimeout) {
        const timeoutInput = document.getElementById('requestTimeout');
        if (timeoutInput) timeoutInput.value = savedTimeout;
    }

    if (savedRefreshInterval) {
        const intervalInput = document.getElementById('refreshInterval');
        if (intervalInput) intervalInput.value = savedRefreshInterval;
    }

    if (savedTheme) {
        const themeSelect = document.getElementById('chartTheme');
        if (themeSelect) themeSelect.value = savedTheme;
    }
}

function saveSettings() {
    const endpointInput = document.getElementById('apiEndpoint');
    const timeoutInput = document.getElementById('requestTimeout');
    const intervalInput = document.getElementById('refreshInterval');
    const themeSelect = document.getElementById('chartTheme');

    if (endpointInput) {
        apiEndpoint = endpointInput.value;
        localStorage.setItem('apiEndpoint', apiEndpoint);
    }

    if (timeoutInput) {
        localStorage.setItem('requestTimeout', timeoutInput.value);
    }

    if (intervalInput) {
        const newInterval = parseInt(intervalInput.value);
        localStorage.setItem('refreshInterval', newInterval);

        // Restart auto-refresh with new interval
        if (refreshInterval) {
            clearInterval(refreshInterval);
        }
        startAutoRefresh(newInterval);
    }

    if (themeSelect) {
        localStorage.setItem('chartTheme', themeSelect.value);
    }

    showNotification('Settings saved successfully!', 'success');
    checkApiStatus(); // Re-check API with new endpoint
}

function resetSettings() {
    localStorage.removeItem('apiEndpoint');
    localStorage.removeItem('requestTimeout');
    localStorage.removeItem('refreshInterval');
    localStorage.removeItem('chartTheme');

    // Reset to defaults
    apiEndpoint = 'http://localhost:8001';

    const endpointInput = document.getElementById('apiEndpoint');
    const timeoutInput = document.getElementById('requestTimeout');
    const intervalInput = document.getElementById('refreshInterval');
    const themeSelect = document.getElementById('chartTheme');

    if (endpointInput) endpointInput.value = 'http://localhost:8001';
    if (timeoutInput) timeoutInput.value = '30';
    if (intervalInput) intervalInput.value = '30';
    if (themeSelect) themeSelect.value = 'light';

    showNotification('Settings reset to defaults', 'info');

    // Restart auto-refresh
    if (refreshInterval) {
        clearInterval(refreshInterval);
    }
    startAutoRefresh();
}

// Utility Functions
function showLoading(message = 'Loading...') {
    const overlay = document.getElementById('loadingOverlay');
    const text = document.querySelector('.loading-text');

    if (overlay) {
        overlay.style.display = 'flex';
    }

    if (text) {
        text.textContent = message;
    }
}

function hideLoading() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.style.display = 'none';
    }
}

function showNotification(message, type = 'info') {
    const container = document.getElementById('notificationContainer');
    if (!container) return;

    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; color: #6c757d; cursor: pointer; font-size: 1.2rem;">&times;</button>
        </div>
    `;

    container.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

function startAutoRefresh(interval = 30) {
    if (refreshInterval) {
        clearInterval(refreshInterval);
    }

    refreshInterval = setInterval(() => {
        if (document.querySelector('.tab-content.active').id === 'dashboard') {
            refreshSystemStatus();
        }
    }, interval * 1000);
}

function stopTraining() {
    isTraining = false;
    updateTrainingUI(false);
    showNotification('Training stopped', 'warning');
}

function downloadChart() {
    if (currentChart) {
        const url = currentChart.toBase64Image();
        const link = document.createElement('a');
        link.download = 'forecast-chart.png';
        link.href = url;
        link.click();
        showNotification('Chart downloaded', 'success');
    } else {
        showNotification('No chart to download', 'warning');
    }
}

function fullscreenChart() {
    const chartContainer = document.querySelector('.chart-container');
    if (chartContainer) {
        if (chartContainer.requestFullscreen) {
            chartContainer.requestFullscreen();
        } else if (chartContainer.webkitRequestFullscreen) {
            chartContainer.webkitRequestFullscreen();
        } else if (chartContainer.msRequestFullscreen) {
            chartContainer.msRequestFullscreen();
        }
    }
}

// Error Handling
window.addEventListener('error', function(event) {
    console.error('Global error:', event.error);
    showNotification('An unexpected error occurred', 'error');
});

window.addEventListener('unhandledrejection', function(event) {
    console.error('Unhandled promise rejection:', event.reason);
    showNotification('An unexpected error occurred', 'error');
});

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
    }

    if (currentChart) {
        currentChart.destroy();
    }
});

console.log('🎉 Enhanced ARIMA Dashboard JavaScript loaded successfully!');
