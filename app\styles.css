/* Enhanced ARIMA Forecasting Dashboard Styles */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo i {
    font-size: 2.5rem;
    color: #667eea;
}

.logo h1 {
    font-size: 2rem;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-info {
    display: flex;
    align-items: center;
    gap: 20px;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: #f8f9fa;
    border-radius: 20px;
    font-size: 0.9rem;
}

.status-indicator.connected {
    background: #d4edda;
    color: #155724;
}

.status-indicator.disconnected {
    background: #f8d7da;
    color: #721c24;
}

.status-indicator i {
    font-size: 0.8rem;
}

.version {
    background: #667eea;
    color: white;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

/* Navigation Tabs */
.nav-tabs {
    display: flex;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 10px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    gap: 5px;
}

.nav-tab {
    flex: 1;
    background: transparent;
    border: none;
    padding: 15px 20px;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.nav-tab:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.nav-tab.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

/* Tab Content */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Grid Layouts */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
}

.models-grid,
.forecasting-grid,
.analytics-grid,
.settings-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

@media (max-width: 768px) {
    .models-grid,
    .forecasting-grid,
    .analytics-grid,
    .settings-grid {
        grid-template-columns: 1fr;
    }
}

/* Card Styles */
.card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 20px;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 10px;
}

.card-header i {
    color: #667eea;
}

.card-content {
    padding: 20px;
}

/* Button Styles */
.btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn.primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.btn.secondary {
    background: linear-gradient(135deg, #6c757d, #495057);
}

.btn.tertiary {
    background: linear-gradient(135deg, #17a2b8, #138496);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.refresh-btn,
.chart-btn {
    background: transparent;
    border: 1px solid #dee2e6;
    color: #6c757d;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.refresh-btn:hover,
.chart-btn:hover {
    background: #f8f9fa;
    color: #495057;
}

/* Form Elements */
.form-select,
.form-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    background: white;
}

.form-select:focus,
.form-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.option-group {
    margin-bottom: 20px;
}

.option-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #495057;
}

/* Status Grid */
.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.status-item {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
}

.status-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 8px;
}

.status-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.action-btn {
    background: white;
    border: 2px solid #e9ecef;
    padding: 20px;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 15px;
    text-align: left;
}

.action-btn:hover {
    border-color: #667eea;
    background: #f8f9fa;
    transform: translateY(-2px);
}

.action-btn.primary {
    border-color: #667eea;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
}

.action-btn i {
    font-size: 1.5rem;
    color: #667eea;
}

.action-btn span {
    font-weight: 600;
    color: #495057;
}

/* Performance Metrics */
.performance-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
}

.metric-item {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
}

.metric-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 8px;
}

.metric-value {
    font-size: 1.3rem;
    font-weight: 700;
    color: #495057;
}

/* Loading States */
.loading-state,
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.loading-state i,
.empty-state i {
    font-size: 3rem;
    margin-bottom: 15px;
    color: #dee2e6;
}

.empty-state p {
    margin-bottom: 15px;
}

.btn-link {
    background: none;
    border: none;
    color: #667eea;
    text-decoration: underline;
    cursor: pointer;
    font-size: 1rem;
}

/* Progress Bar */
.progress-bar {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    text-align: center;
    color: #6c757d;
    font-size: 0.9rem;
}

/* Chart Container */
.chart-container {
    position: relative;
    height: 400px;
    margin-bottom: 20px;
}

.chart-controls {
    display: flex;
    gap: 10px;
}

/* Summary Grid */
.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.summary-item {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
}

.summary-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 8px;
}

.summary-value {
    font-size: 1.2rem;
    font-weight: 600;
    color: #495057;
}

/* Analytics Tabs */
.analytics-tabs {
    display: flex;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 4px;
    margin-bottom: 20px;
}

.analytics-tab {
    flex: 1;
    background: transparent;
    border: none;
    padding: 10px 15px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    color: #6c757d;
}

.analytics-tab:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.analytics-tab.active {
    background: white;
    color: #495057;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.analytics-content {
    display: none;
}

.analytics-content.active {
    display: block;
}

/* Insights */
.insights-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.insight-item {
    display: flex;
    gap: 15px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
    border-left: 4px solid #667eea;
}

.insight-icon {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.insight-content h4 {
    margin-bottom: 8px;
    color: #495057;
    font-size: 1.1rem;
}

.insight-content p {
    color: #6c757d;
    line-height: 1.5;
}

/* Info Grid */
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.info-item {
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    text-align: center;
}

.info-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 8px;
}

.info-value {
    font-size: 1rem;
    font-weight: 600;
    color: #495057;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-content {
    background: white;
    padding: 40px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
}

/* Notifications */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.notification {
    background: white;
    border-radius: 8px;
    padding: 15px 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #667eea;
    min-width: 300px;
    animation: slideIn 0.3s ease;
}

.notification.success {
    border-left-color: #28a745;
}

.notification.error {
    border-left-color: #dc3545;
}

.notification.warning {
    border-left-color: #ffc107;
}

.notification.info {
    border-left-color: #17a2b8;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Training Controls */
.training-controls {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.forecast-controls {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.settings-controls {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

/* Model List */
.model-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.model-item {
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #667eea;
}

.model-name {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
}

.model-status {
    font-size: 0.9rem;
    color: #6c757d;
}

.model-metrics {
    display: flex;
    gap: 15px;
    margin-top: 10px;
}

.model-metric {
    font-size: 0.8rem;
    color: #6c757d;
}

/* Forecast List */
.forecast-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.forecast-item {
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #17a2b8;
}

.forecast-title {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
}

.forecast-details {
    font-size: 0.9rem;
    color: #6c757d;
}

/* Results Grid */
.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.result-item {
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    text-align: center;
}

.result-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 8px;
}

.result-value {
    font-size: 1.2rem;
    font-weight: 600;
    color: #495057;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .container {
        padding: 15px;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .nav-tabs {
        flex-direction: column;
    }

    .nav-tab {
        justify-content: flex-start;
    }

    .action-buttons {
        gap: 10px;
    }

    .training-controls,
    .forecast-controls,
    .settings-controls {
        flex-direction: column;
    }

    .chart-container {
        height: 300px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 10px;
    }

    .card-content {
        padding: 15px;
    }

    .status-grid,
    .performance-metrics,
    .summary-grid,
    .info-grid,
    .results-grid {
        grid-template-columns: 1fr;
    }
}
