"""
Dynamic ARIMA Training System for Multiple Business Types
========================================================

A comprehensive, production-ready ARIMA training system that can automatically:
- Classify business types (F&B, Retail, Services, etc.)
- Apply business-specific preprocessing and feature engineering
- Train optimized ARIMA models for each business type
- Provide intelligent model selection and performance evaluation
- Support multiple revenue streams and forecasting scenarios

Author: AI Assistant
Date: 2025-06-29
Version: 1.0.0
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any, Union
from dataclasses import dataclass, field
from enum import Enum
import json
import pickle
import warnings
from pathlib import Path
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from functools import lru_cache

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('dynamic_arima_trainer.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Import required libraries with fallbacks
try:
    from statsmodels.tsa.arima.model import ARIMA
    from statsmodels.tsa.stattools import adfuller, kpss
    from statsmodels.tsa.seasonal import seasonal_decompose
    from statsmodels.stats.diagnostic import acorr_ljungbox
    from statsmodels.graphics.tsaplots import plot_acf, plot_pacf
    STATSMODELS_AVAILABLE = True
except ImportError:
    logger.warning("statsmodels not available. Please install: pip install statsmodels")
    STATSMODELS_AVAILABLE = False

try:
    import pmdarima as pm
    from pmdarima import auto_arima
    AUTO_ARIMA_AVAILABLE = True
except (ImportError, ValueError) as e:
    logger.warning(f"pmdarima not available or incompatible: {e}")
    logger.warning("Falling back to manual ARIMA training only")
    AUTO_ARIMA_AVAILABLE = False

try:
    from sklearn.preprocessing import StandardScaler, MinMaxScaler
    from sklearn.cluster import KMeans
    from sklearn.metrics import silhouette_score
    SKLEARN_AVAILABLE = True
except ImportError:
    logger.warning("scikit-learn not available. Please install: pip install scikit-learn")
    SKLEARN_AVAILABLE = False

try:
    import scipy.stats as stats
    from scipy.optimize import minimize
    SCIPY_AVAILABLE = True
except ImportError:
    logger.warning("scipy not available. Please install: pip install scipy")
    SCIPY_AVAILABLE = False


class BusinessType(Enum):
    """Enumeration of supported business types"""
    FOOD_AND_BEVERAGE = "F&B"
    RETAIL_GENERAL = "Retail"
    RETAIL_FASHION = "Fashion"
    RETAIL_ELECTRONICS = "Electronics"
    RETAIL_HOME_GARDEN = "Home & Garden"
    SERVICES_PROFESSIONAL = "Professional Services"
    SERVICES_PERSONAL = "Personal Services"
    HOSPITALITY = "Hospitality"
    HEALTHCARE = "Healthcare"
    AUTOMOTIVE = "Automotive"
    ENTERTAINMENT = "Entertainment"
    EDUCATION = "Education"
    UNKNOWN = "Unknown"


@dataclass
class BusinessCharacteristics:
    """Data class for business-specific characteristics"""
    business_type: BusinessType
    seasonality_patterns: List[str] = field(default_factory=list)  # ['weekly', 'monthly', 'yearly']
    peak_periods: List[str] = field(default_factory=list)  # ['holiday', 'weekend', 'summer']
    volatility_level: str = "medium"  # 'low', 'medium', 'high'
    trend_stability: str = "stable"  # 'stable', 'growing', 'declining', 'volatile'
    customer_behavior: str = "regular"  # 'regular', 'impulse', 'seasonal', 'event_driven'
    price_sensitivity: str = "medium"  # 'low', 'medium', 'high'
    inventory_turnover: str = "medium"  # 'low', 'medium', 'high'
    recommended_forecast_horizon: int = 30  # days
    min_data_points: int = 60  # minimum data points needed
    preferred_aggregation: str = "daily"  # 'hourly', 'daily', 'weekly', 'monthly'


@dataclass
class ModelConfiguration:
    """Configuration for ARIMA model training"""
    max_p: int = 5
    max_d: int = 2
    max_q: int = 5
    seasonal: bool = False
    seasonal_periods: int = 7
    use_auto_arima: bool = True
    use_ensemble: bool = False
    validation_split: float = 0.2
    cross_validation_folds: int = 3
    optimization_metric: str = "aic"  # 'aic', 'bic', 'mape', 'rmse'
    max_training_time: int = 300  # seconds
    enable_parallel: bool = True
    n_jobs: int = -1


@dataclass
class ModelPerformance:
    """Model performance metrics"""
    business_type: BusinessType
    model_params: Tuple[int, int, int]
    aic: float
    bic: float
    mae: float
    mse: float
    rmse: float
    mape: float
    training_time: float
    prediction_time: float
    validation_score: float
    confidence_intervals: bool = True
    residual_analysis: Dict[str, float] = field(default_factory=dict)


class BusinessTypeClassifier:
    """Intelligent business type classification based on transaction patterns"""
    
    def __init__(self):
        self.classification_rules = self._initialize_classification_rules()
        self.feature_extractors = self._initialize_feature_extractors()
    
    def _initialize_classification_rules(self) -> Dict[str, Dict]:
        """Initialize business classification rules"""
        return {
            'product_keywords': {
                BusinessType.FOOD_AND_BEVERAGE: [
                    'coffee', 'tea', 'food', 'drink', 'beverage', 'restaurant', 'cafe',
                    'meal', 'snack', 'juice', 'wine', 'beer', 'alcohol', 'dining'
                ],
                BusinessType.RETAIL_FASHION: [
                    'shirt', 'dress', 'pants', 'shoes', 'jacket', 'hat', 'clothing',
                    'fashion', 'apparel', 'accessories', 'jewelry', 'watch', 'bag'
                ],
                BusinessType.RETAIL_ELECTRONICS: [
                    'phone', 'computer', 'laptop', 'tablet', 'camera', 'headphones',
                    'electronics', 'gadget', 'device', 'charger', 'cable', 'speaker'
                ],
                BusinessType.RETAIL_HOME_GARDEN: [
                    'furniture', 'home', 'garden', 'plant', 'decoration', 'kitchen',
                    'bathroom', 'bedroom', 'living', 'outdoor', 'tools', 'appliance'
                ],
                BusinessType.RETAIL_GENERAL: [
                    'general', 'store', 'shop', 'retail', 'merchandise', 'goods',
                    'product', 'item', 'sale', 'discount', 'clearance'
                ]
            },
            'transaction_patterns': {
                'peak_hours': {
                    BusinessType.FOOD_AND_BEVERAGE: [7, 8, 12, 13, 18, 19, 20],
                    BusinessType.RETAIL_GENERAL: [10, 11, 14, 15, 16, 17, 18, 19],
                    BusinessType.SERVICES_PROFESSIONAL: [9, 10, 11, 14, 15, 16, 17]
                },
                'peak_days': {
                    BusinessType.RETAIL_GENERAL: [4, 5, 6],  # Friday, Saturday, Sunday
                    BusinessType.FOOD_AND_BEVERAGE: [4, 5, 6],
                    BusinessType.SERVICES_PROFESSIONAL: [0, 1, 2, 3, 4]  # Weekdays
                }
            }
        }
    
    def _initialize_feature_extractors(self) -> Dict[str, callable]:
        """Initialize feature extraction functions"""
        return {
            'hourly_distribution': self._extract_hourly_distribution,
            'daily_distribution': self._extract_daily_distribution,
            'price_distribution': self._extract_price_distribution,
            'quantity_patterns': self._extract_quantity_patterns,
            'seasonality_strength': self._extract_seasonality_strength
        }

    def classify_business_type(self, df: pd.DataFrame) -> Tuple[BusinessType, float]:
        """
        Classify business type based on transaction data

        Args:
            df: DataFrame with transaction data

        Returns:
            Tuple of (BusinessType, confidence_score)
        """
        try:
            logger.info("🔍 Analyzing transaction patterns for business type classification...")

            # Extract features
            features = self._extract_all_features(df)

            # Score each business type
            business_scores = {}

            for business_type in BusinessType:
                if business_type == BusinessType.UNKNOWN:
                    continue

                score = self._calculate_business_score(features, business_type)
                business_scores[business_type] = score

            # Find best match
            best_business_type = max(business_scores, key=business_scores.get)
            confidence = business_scores[best_business_type]

            # If confidence is too low, classify as unknown
            if confidence < 0.3:
                best_business_type = BusinessType.UNKNOWN
                confidence = 0.0

            logger.info(f"✅ Business type classified as: {best_business_type.value} (confidence: {confidence:.2f})")

            return best_business_type, confidence

        except Exception as e:
            logger.error(f"❌ Business type classification failed: {e}")
            return BusinessType.UNKNOWN, 0.0

    def _extract_all_features(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Extract all features from transaction data"""
        features = {}

        # Extract product keywords
        if 'itemdescription' in df.columns:
            features['product_keywords'] = df['itemdescription'].dropna().tolist()
        else:
            features['product_keywords'] = []

        for feature_name, extractor in self.feature_extractors.items():
            try:
                features[feature_name] = extractor(df)
            except Exception as e:
                logger.warning(f"⚠️ Failed to extract {feature_name}: {e}")
                features[feature_name] = None

        return features

    def _calculate_business_score(self, features: Dict[str, Any], business_type: BusinessType) -> float:
        """Calculate score for a specific business type"""
        score = 0.0
        total_weight = 0.0

        # Product keyword matching
        if 'product_keywords' in features and features['product_keywords']:
            keyword_score = self._score_product_keywords(features['product_keywords'], business_type)
            score += keyword_score * 0.4
            total_weight += 0.4

        # Hourly pattern matching
        if 'hourly_distribution' in features and features['hourly_distribution'] is not None:
            hourly_score = self._score_hourly_patterns(features['hourly_distribution'], business_type)
            score += hourly_score * 0.3
            total_weight += 0.3

        # Daily pattern matching
        if 'daily_distribution' in features and features['daily_distribution'] is not None:
            daily_score = self._score_daily_patterns(features['daily_distribution'], business_type)
            score += daily_score * 0.2
            total_weight += 0.2

        # Price distribution matching
        if 'price_distribution' in features and features['price_distribution'] is not None:
            price_score = self._score_price_patterns(features['price_distribution'], business_type)
            score += price_score * 0.1
            total_weight += 0.1

        return score / total_weight if total_weight > 0 else 0.0

    def _extract_hourly_distribution(self, df: pd.DataFrame) -> Optional[Dict[int, float]]:
        """Extract hourly transaction distribution"""
        try:
            if 'transaction_hour' in df.columns:
                hourly_dist = df['transaction_hour'].value_counts(normalize=True).to_dict()
                return hourly_dist
            elif 'transactiontime' in df.columns:
                df['hour'] = pd.to_datetime(df['transactiontime']).dt.hour
                hourly_dist = df['hour'].value_counts(normalize=True).to_dict()
                return hourly_dist
            return None
        except Exception:
            return None

    def _extract_daily_distribution(self, df: pd.DataFrame) -> Optional[Dict[int, float]]:
        """Extract daily transaction distribution (weekday)"""
        try:
            if 'transaction_weekday' in df.columns:
                daily_dist = df['transaction_weekday'].value_counts(normalize=True).to_dict()
                return daily_dist
            elif 'transactiontime' in df.columns:
                df['weekday'] = pd.to_datetime(df['transactiontime']).dt.dayofweek
                daily_dist = df['weekday'].value_counts(normalize=True).to_dict()
                return daily_dist
            return None
        except Exception:
            return None

    def _extract_price_distribution(self, df: pd.DataFrame) -> Optional[Dict[str, float]]:
        """Extract price distribution characteristics"""
        try:
            price_col = None
            if 'costperitem' in df.columns:
                price_col = 'costperitem'
            elif 'total_transaction_value' in df.columns:
                price_col = 'total_transaction_value'

            if price_col:
                prices = df[price_col].dropna()
                return {
                    'mean': float(prices.mean()),
                    'median': float(prices.median()),
                    'std': float(prices.std()),
                    'min': float(prices.min()),
                    'max': float(prices.max()),
                    'q25': float(prices.quantile(0.25)),
                    'q75': float(prices.quantile(0.75))
                }
            return None
        except Exception:
            return None

    def _extract_quantity_patterns(self, df: pd.DataFrame) -> Optional[Dict[str, float]]:
        """Extract quantity purchase patterns"""
        try:
            if 'numberofitemspurchased' in df.columns:
                quantities = df['numberofitemspurchased'].dropna()
                return {
                    'mean': float(quantities.mean()),
                    'median': float(quantities.median()),
                    'mode': float(quantities.mode().iloc[0] if not quantities.mode().empty else 1),
                    'bulk_purchases_pct': float((quantities > 10).mean())
                }
            return None
        except Exception:
            return None

    def _extract_seasonality_strength(self, df: pd.DataFrame) -> Optional[float]:
        """Extract seasonality strength from time series"""
        try:
            if 'transactiontime' in df.columns and 'total_transaction_value' in df.columns:
                # Create daily time series
                df['date'] = pd.to_datetime(df['transactiontime']).dt.date
                daily_revenue = df.groupby('date')['total_transaction_value'].sum()

                if len(daily_revenue) > 14:  # Need at least 2 weeks
                    # Simple seasonality measure using autocorrelation
                    autocorr_7 = daily_revenue.autocorr(lag=7)  # Weekly seasonality
                    return float(abs(autocorr_7)) if not pd.isna(autocorr_7) else 0.0
            return 0.0
        except Exception:
            return 0.0

    def _score_product_keywords(self, product_descriptions: List[str], business_type: BusinessType) -> float:
        """Score based on product keyword matching"""
        if business_type not in self.classification_rules['product_keywords']:
            return 0.0

        keywords = self.classification_rules['product_keywords'][business_type]
        total_matches = 0
        total_products = len(product_descriptions)

        for description in product_descriptions:
            if description and isinstance(description, str):
                description_lower = description.lower()
                for keyword in keywords:
                    if keyword in description_lower:
                        total_matches += 1
                        break

        return total_matches / total_products if total_products > 0 else 0.0

    def _score_hourly_patterns(self, hourly_dist: Dict[int, float], business_type: BusinessType) -> float:
        """Score based on hourly transaction patterns"""
        if business_type not in self.classification_rules['transaction_patterns']['peak_hours']:
            return 0.5  # Neutral score

        expected_peak_hours = self.classification_rules['transaction_patterns']['peak_hours'][business_type]

        # Calculate score based on how much activity occurs during expected peak hours
        peak_activity = sum(hourly_dist.get(hour, 0) for hour in expected_peak_hours)
        return min(peak_activity * 2, 1.0)  # Scale to 0-1

    def _score_daily_patterns(self, daily_dist: Dict[int, float], business_type: BusinessType) -> float:
        """Score based on daily transaction patterns"""
        if business_type not in self.classification_rules['transaction_patterns']['peak_days']:
            return 0.5  # Neutral score

        expected_peak_days = self.classification_rules['transaction_patterns']['peak_days'][business_type]

        # Calculate score based on how much activity occurs during expected peak days
        peak_activity = sum(daily_dist.get(day, 0) for day in expected_peak_days)
        return min(peak_activity * 1.5, 1.0)  # Scale to 0-1

    def _score_price_patterns(self, price_dist: Dict[str, float], business_type: BusinessType) -> float:
        """Score based on price distribution patterns"""
        # Simple heuristic scoring based on typical price ranges for different business types
        mean_price = price_dist.get('mean', 0)

        price_ranges = {
            BusinessType.FOOD_AND_BEVERAGE: (5, 50),
            BusinessType.RETAIL_FASHION: (10, 200),
            BusinessType.RETAIL_ELECTRONICS: (20, 1000),
            BusinessType.RETAIL_HOME_GARDEN: (15, 500),
            BusinessType.RETAIL_GENERAL: (1, 100)
        }

        if business_type in price_ranges:
            min_price, max_price = price_ranges[business_type]
            if min_price <= mean_price <= max_price:
                return 1.0
            elif mean_price < min_price:
                return max(0.0, 1.0 - (min_price - mean_price) / min_price)
            else:
                return max(0.0, 1.0 - (mean_price - max_price) / max_price)

        return 0.5  # Neutral score


class BusinessCharacteristicsManager:
    """Manages business-specific characteristics and configurations"""

    def __init__(self):
        self.characteristics_db = self._initialize_characteristics_db()

    def _initialize_characteristics_db(self) -> Dict[BusinessType, BusinessCharacteristics]:
        """Initialize business characteristics database"""
        return {
            BusinessType.FOOD_AND_BEVERAGE: BusinessCharacteristics(
                business_type=BusinessType.FOOD_AND_BEVERAGE,
                seasonality_patterns=['weekly', 'daily', 'holiday'],
                peak_periods=['weekend', 'lunch_hours', 'dinner_hours', 'holiday'],
                volatility_level='medium',
                trend_stability='stable',
                customer_behavior='regular',
                price_sensitivity='medium',
                inventory_turnover='high',
                recommended_forecast_horizon=14,
                min_data_points=42,
                preferred_aggregation='daily'
            ),
            BusinessType.RETAIL_GENERAL: BusinessCharacteristics(
                business_type=BusinessType.RETAIL_GENERAL,
                seasonality_patterns=['weekly', 'monthly', 'yearly'],
                peak_periods=['weekend', 'holiday', 'seasonal_sales'],
                volatility_level='medium',
                trend_stability='stable',
                customer_behavior='impulse',
                price_sensitivity='high',
                inventory_turnover='medium',
                recommended_forecast_horizon=30,
                min_data_points=60,
                preferred_aggregation='daily'
            ),
            BusinessType.RETAIL_FASHION: BusinessCharacteristics(
                business_type=BusinessType.RETAIL_FASHION,
                seasonality_patterns=['weekly', 'monthly', 'yearly'],
                peak_periods=['weekend', 'seasonal_change', 'holiday'],
                volatility_level='high',
                trend_stability='volatile',
                customer_behavior='seasonal',
                price_sensitivity='medium',
                inventory_turnover='low',
                recommended_forecast_horizon=45,
                min_data_points=90,
                preferred_aggregation='weekly'
            ),
            BusinessType.RETAIL_ELECTRONICS: BusinessCharacteristics(
                business_type=BusinessType.RETAIL_ELECTRONICS,
                seasonality_patterns=['monthly', 'yearly'],
                peak_periods=['holiday', 'back_to_school', 'product_launches'],
                volatility_level='high',
                trend_stability='volatile',
                customer_behavior='event_driven',
                price_sensitivity='low',
                inventory_turnover='low',
                recommended_forecast_horizon=60,
                min_data_points=120,
                preferred_aggregation='weekly'
            ),
            BusinessType.SERVICES_PROFESSIONAL: BusinessCharacteristics(
                business_type=BusinessType.SERVICES_PROFESSIONAL,
                seasonality_patterns=['weekly', 'monthly'],
                peak_periods=['weekdays', 'month_end', 'quarter_end'],
                volatility_level='low',
                trend_stability='stable',
                customer_behavior='regular',
                price_sensitivity='low',
                inventory_turnover='high',
                recommended_forecast_horizon=30,
                min_data_points=60,
                preferred_aggregation='daily'
            )
        }

    def get_characteristics(self, business_type: BusinessType) -> BusinessCharacteristics:
        """Get characteristics for a specific business type"""
        return self.characteristics_db.get(
            business_type,
            self.characteristics_db[BusinessType.RETAIL_GENERAL]  # Default fallback
        )


class DynamicARIMATrainer:
    """
    Main dynamic ARIMA training system that handles multiple business types
    """

    def __init__(self, config: Optional[ModelConfiguration] = None):
        """
        Initialize the dynamic ARIMA trainer

        Args:
            config: Model configuration (uses defaults if None)
        """
        self.config = config or ModelConfiguration()
        self.business_classifier = BusinessTypeClassifier()
        self.characteristics_manager = BusinessCharacteristicsManager()

        # Training state
        self.trained_models: Dict[BusinessType, Dict] = {}
        self.model_performances: Dict[BusinessType, ModelPerformance] = {}
        self.training_history: List[Dict] = []

        # Data storage
        self.raw_data: Optional[pd.DataFrame] = None
        self.processed_data: Dict[BusinessType, pd.Series] = {}
        self.business_type: Optional[BusinessType] = None
        self.business_characteristics: Optional[BusinessCharacteristics] = None

        logger.info("🚀 Dynamic ARIMA Trainer initialized")

    def load_data(self, data_source: Union[str, pd.DataFrame, Dict], data_type: str = "auto") -> bool:
        """
        Load data from various sources

        Args:
            data_source: Path to file, DataFrame, or dictionary
            data_type: Type of data source ('csv', 'json', 'dataframe', 'auto')

        Returns:
            bool: Success status
        """
        try:
            logger.info("📊 Loading data for dynamic ARIMA training...")

            if data_type == "auto":
                data_type = self._detect_data_type(data_source)

            if data_type == "csv":
                self.raw_data = pd.read_csv(data_source)
            elif data_type == "json":
                if isinstance(data_source, str):
                    with open(data_source, 'r') as f:
                        json_data = json.load(f)
                else:
                    json_data = data_source

                if 'transactions' in json_data:
                    self.raw_data = pd.DataFrame(json_data['transactions'])
                else:
                    self.raw_data = pd.DataFrame(json_data)
            elif data_type == "dataframe":
                self.raw_data = data_source.copy()
            else:
                raise ValueError(f"Unsupported data type: {data_type}")

            logger.info(f"✅ Loaded {len(self.raw_data)} records")
            logger.info(f"📋 Columns: {list(self.raw_data.columns)}")

            return True

        except Exception as e:
            logger.error(f"❌ Failed to load data: {e}")
            return False

    def analyze_and_classify(self) -> Tuple[BusinessType, float]:
        """
        Analyze data and classify business type

        Returns:
            Tuple of (BusinessType, confidence_score)
        """
        if self.raw_data is None:
            raise ValueError("No data loaded. Call load_data() first.")

        logger.info("🔍 Analyzing business type and characteristics...")

        # Classify business type
        self.business_type, confidence = self.business_classifier.classify_business_type(self.raw_data)

        # Get business characteristics
        self.business_characteristics = self.characteristics_manager.get_characteristics(self.business_type)

        logger.info(f"📊 Business Type: {self.business_type.value}")
        logger.info(f"🎯 Classification Confidence: {confidence:.2f}")
        logger.info(f"📈 Recommended Forecast Horizon: {self.business_characteristics.recommended_forecast_horizon} days")
        logger.info(f"⚡ Volatility Level: {self.business_characteristics.volatility_level}")

        return self.business_type, confidence

    def preprocess_data(self) -> bool:
        """
        Preprocess data based on business type characteristics

        Returns:
            bool: Success status
        """
        try:
            if self.raw_data is None or self.business_type is None:
                raise ValueError("Data not loaded or business type not classified")

            logger.info("🔧 Preprocessing data based on business characteristics...")

            # Convert datetime column
            datetime_col = self._identify_datetime_column()
            if datetime_col:
                self.raw_data[datetime_col] = pd.to_datetime(self.raw_data[datetime_col])
            else:
                raise ValueError("No datetime column found")

            # Identify revenue column
            revenue_col = self._identify_revenue_column()
            if not revenue_col:
                raise ValueError("No revenue column found")

            # Create time series based on business characteristics
            aggregation = self.business_characteristics.preferred_aggregation
            time_series = self._create_time_series(datetime_col, revenue_col, aggregation)

            # Apply business-specific preprocessing
            time_series = self._apply_business_specific_preprocessing(time_series)

            # Store processed data
            self.processed_data[self.business_type] = time_series

            logger.info(f"✅ Preprocessed {len(time_series)} {aggregation} data points")
            logger.info(f"📅 Date range: {time_series.index.min()} to {time_series.index.max()}")
            logger.info(f"💰 Revenue range: ${time_series.min():.2f} to ${time_series.max():.2f}")

            return True

        except Exception as e:
            logger.error(f"❌ Data preprocessing failed: {e}")
            return False

    def train_model(self, business_type: Optional[BusinessType] = None) -> bool:
        """
        Train ARIMA model for specified business type

        Args:
            business_type: Business type to train for (uses classified type if None)

        Returns:
            bool: Success status
        """
        try:
            target_business_type = business_type or self.business_type

            if target_business_type not in self.processed_data:
                raise ValueError(f"No processed data for business type: {target_business_type}")

            logger.info(f"🤖 Training ARIMA model for {target_business_type.value}...")

            time_series = self.processed_data[target_business_type]
            characteristics = self.characteristics_manager.get_characteristics(target_business_type)

            # Check minimum data requirements
            if len(time_series) < characteristics.min_data_points:
                logger.warning(f"⚠️ Insufficient data points: {len(time_series)} < {characteristics.min_data_points}")

            # Configure model based on business characteristics
            model_config = self._create_business_specific_config(characteristics)

            # Train model
            start_time = time.time()

            if model_config.use_auto_arima and AUTO_ARIMA_AVAILABLE:
                trained_model = self._train_auto_arima(time_series, model_config)
            else:
                trained_model = self._train_manual_arima(time_series, model_config)

            training_time = time.time() - start_time

            if trained_model is None:
                raise Exception("Model training failed")

            # Evaluate model performance
            performance = self._evaluate_model_performance(
                trained_model, time_series, target_business_type, training_time
            )

            # Store results
            self.trained_models[target_business_type] = {
                'model': trained_model,
                'config': model_config,
                'training_time': training_time,
                'data_points': len(time_series)
            }
            self.model_performances[target_business_type] = performance

            # Add to training history
            self.training_history.append({
                'timestamp': datetime.now(),
                'business_type': target_business_type.value,
                'performance': performance,
                'training_time': training_time
            })

            logger.info(f"✅ Model trained successfully!")
            logger.info(f"📊 ARIMA{performance.model_params} - AIC: {performance.aic:.2f}")
            logger.info(f"🎯 MAPE: {performance.mape:.2f}% - RMSE: {performance.rmse:.2f}")
            logger.info(f"⏱️ Training time: {training_time:.2f}s")

            return True

        except Exception as e:
            logger.error(f"❌ Model training failed: {e}")
            return False

    def forecast(self, steps: int = None, business_type: Optional[BusinessType] = None) -> Dict[str, Any]:
        """
        Generate forecasts using trained model

        Args:
            steps: Number of periods to forecast (uses business recommendation if None)
            business_type: Business type to forecast for (uses classified type if None)

        Returns:
            Dictionary with forecast results
        """
        try:
            target_business_type = business_type or self.business_type

            if target_business_type not in self.trained_models:
                raise ValueError(f"No trained model for business type: {target_business_type}")

            characteristics = self.characteristics_manager.get_characteristics(target_business_type)
            forecast_steps = steps or characteristics.recommended_forecast_horizon

            logger.info(f"🔮 Generating {forecast_steps}-step forecast for {target_business_type.value}...")

            model_info = self.trained_models[target_business_type]
            trained_model = model_info['model']

            start_time = time.time()

            # Generate forecast
            if hasattr(trained_model, 'forecast'):
                # statsmodels ARIMA
                forecast_result = trained_model.forecast(steps=forecast_steps)
                conf_int = trained_model.get_forecast(steps=forecast_steps).conf_int()

                forecast_values = forecast_result.values if hasattr(forecast_result, 'values') else forecast_result
                lower_bounds = conf_int.iloc[:, 0].values
                upper_bounds = conf_int.iloc[:, 1].values

            elif hasattr(trained_model, 'predict'):
                # pmdarima auto_arima
                forecast_values, conf_int = trained_model.predict(n_periods=forecast_steps, return_conf_int=True)
                lower_bounds = conf_int[:, 0]
                upper_bounds = conf_int[:, 1]
            else:
                raise ValueError("Unsupported model type for forecasting")

            prediction_time = time.time() - start_time

            # Create forecast dates
            last_date = self.processed_data[target_business_type].index[-1]
            if characteristics.preferred_aggregation == 'daily':
                forecast_dates = pd.date_range(start=last_date + timedelta(days=1), periods=forecast_steps, freq='D')
            elif characteristics.preferred_aggregation == 'weekly':
                forecast_dates = pd.date_range(start=last_date + timedelta(weeks=1), periods=forecast_steps, freq='W')
            else:
                forecast_dates = pd.date_range(start=last_date + timedelta(days=1), periods=forecast_steps, freq='D')

            # Prepare results
            forecast_result = {
                'business_type': target_business_type.value,
                'forecast_horizon': forecast_steps,
                'aggregation': characteristics.preferred_aggregation,
                'forecast_dates': forecast_dates.tolist(),
                'forecast_values': forecast_values.tolist(),
                'lower_bounds': lower_bounds.tolist(),
                'upper_bounds': upper_bounds.tolist(),
                'prediction_time': prediction_time,
                'model_params': self.model_performances[target_business_type].model_params,
                'confidence_level': 0.95,
                'generated_at': datetime.now().isoformat()
            }

            logger.info(f"✅ Forecast generated successfully!")
            logger.info(f"📈 Forecast range: ${min(forecast_values):.2f} to ${max(forecast_values):.2f}")
            logger.info(f"⏱️ Prediction time: {prediction_time:.3f}s")

            return forecast_result

        except Exception as e:
            logger.error(f"❌ Forecasting failed: {e}")
            return {}

    def _detect_data_type(self, data_source: Any) -> str:
        """Detect the type of data source"""
        if isinstance(data_source, pd.DataFrame):
            return "dataframe"
        elif isinstance(data_source, str):
            if data_source.endswith('.csv'):
                return "csv"
            elif data_source.endswith('.json'):
                return "json"
        elif isinstance(data_source, dict):
            return "json"
        return "unknown"

    def _identify_datetime_column(self) -> Optional[str]:
        """Identify the datetime column in the data"""
        datetime_candidates = ['transactiontime', 'timestamp', 'date', 'datetime', 'time']

        for col in datetime_candidates:
            if col in self.raw_data.columns:
                return col

        # Check for datetime-like columns
        for col in self.raw_data.columns:
            if self.raw_data[col].dtype == 'object':
                try:
                    pd.to_datetime(self.raw_data[col].iloc[0])
                    return col
                except:
                    continue

        return None

    def _identify_revenue_column(self) -> Optional[str]:
        """Identify the revenue column in the data"""
        revenue_candidates = [
            'total_transaction_value', 'revenue', 'sales', 'amount',
            'value', 'price', 'total', 'income'
        ]

        for col in revenue_candidates:
            if col in self.raw_data.columns:
                return col

        return None

    def _create_time_series(self, datetime_col: str, revenue_col: str, aggregation: str) -> pd.Series:
        """Create time series based on aggregation level"""
        df = self.raw_data.copy()
        df[datetime_col] = pd.to_datetime(df[datetime_col])

        if aggregation == 'daily':
            df['date'] = df[datetime_col].dt.date
            time_series = df.groupby('date')[revenue_col].sum()
            time_series.index = pd.to_datetime(time_series.index)
        elif aggregation == 'weekly':
            df['week'] = df[datetime_col].dt.to_period('W')
            time_series = df.groupby('week')[revenue_col].sum()
            time_series.index = time_series.index.to_timestamp()
        elif aggregation == 'monthly':
            df['month'] = df[datetime_col].dt.to_period('M')
            time_series = df.groupby('month')[revenue_col].sum()
            time_series.index = time_series.index.to_timestamp()
        else:
            # Default to daily
            df['date'] = df[datetime_col].dt.date
            time_series = df.groupby('date')[revenue_col].sum()
            time_series.index = pd.to_datetime(time_series.index)

        return time_series.sort_index()

    def _apply_business_specific_preprocessing(self, time_series: pd.Series) -> pd.Series:
        """Apply business-specific preprocessing"""
        # Remove outliers based on business characteristics
        if self.business_characteristics.volatility_level == 'low':
            # More aggressive outlier removal for stable businesses
            q1 = time_series.quantile(0.25)
            q3 = time_series.quantile(0.75)
            iqr = q3 - q1
            lower_bound = q1 - 2.0 * iqr
            upper_bound = q3 + 2.0 * iqr
        elif self.business_characteristics.volatility_level == 'high':
            # Less aggressive outlier removal for volatile businesses
            q1 = time_series.quantile(0.1)
            q3 = time_series.quantile(0.9)
            iqr = q3 - q1
            lower_bound = q1 - 3.0 * iqr
            upper_bound = q3 + 3.0 * iqr
        else:
            # Medium volatility
            q1 = time_series.quantile(0.25)
            q3 = time_series.quantile(0.75)
            iqr = q3 - q1
            lower_bound = q1 - 2.5 * iqr
            upper_bound = q3 + 2.5 * iqr

        # Apply outlier filtering
        time_series = time_series[(time_series >= lower_bound) & (time_series <= upper_bound)]

        # Fill missing dates if needed
        if len(time_series) > 1:
            full_date_range = pd.date_range(start=time_series.index.min(), end=time_series.index.max(), freq='D')
            time_series = time_series.reindex(full_date_range)

            # Forward fill missing values (business-appropriate)
            time_series = time_series.fillna(method='ffill').fillna(method='bfill')

        return time_series

    def _create_business_specific_config(self, characteristics: BusinessCharacteristics) -> ModelConfiguration:
        """Create model configuration based on business characteristics"""
        config = ModelConfiguration()

        # Adjust parameters based on business type
        if characteristics.volatility_level == 'high':
            config.max_p = 3
            config.max_q = 3
            config.validation_split = 0.3
        elif characteristics.volatility_level == 'low':
            config.max_p = 2
            config.max_q = 2
            config.validation_split = 0.15

        # Enable seasonality if business has seasonal patterns
        if 'weekly' in characteristics.seasonality_patterns:
            config.seasonal = True
            config.seasonal_periods = 7
        elif 'monthly' in characteristics.seasonality_patterns:
            config.seasonal = True
            config.seasonal_periods = 30

        # Adjust optimization metric based on business needs
        if characteristics.customer_behavior == 'impulse':
            config.optimization_metric = 'mape'  # Better for volatile patterns
        else:
            config.optimization_metric = 'aic'   # Better for stable patterns

        return config

    def _train_auto_arima(self, time_series: pd.Series, config: ModelConfiguration) -> Any:
        """Train ARIMA model using auto_arima"""
        try:
            logger.info("🚀 Using auto_arima for model training...")

            model = pm.auto_arima(
                time_series.dropna(),
                start_p=0, start_q=0,
                max_p=config.max_p, max_q=config.max_q, max_d=config.max_d,
                seasonal=config.seasonal,
                m=config.seasonal_periods if config.seasonal else 1,
                stepwise=True,
                suppress_warnings=True,
                error_action='ignore',
                max_iter=50,
                n_jobs=config.n_jobs if config.enable_parallel else 1,
                information_criterion=config.optimization_metric
            )

            return model

        except Exception as e:
            logger.error(f"❌ Auto ARIMA training failed: {e}")
            return None

    def _train_manual_arima(self, time_series: pd.Series, config: ModelConfiguration) -> Any:
        """Train ARIMA model using manual parameter search"""
        try:
            logger.info("🔍 Using manual parameter search for model training...")

            best_model = None
            best_score = float('inf')
            best_params = None

            # Grid search for optimal parameters
            for p in range(config.max_p + 1):
                for d in range(config.max_d + 1):
                    for q in range(config.max_q + 1):
                        try:
                            model = ARIMA(time_series.dropna(), order=(p, d, q))
                            fitted_model = model.fit()

                            # Select based on optimization metric
                            if config.optimization_metric == 'aic':
                                score = fitted_model.aic
                            elif config.optimization_metric == 'bic':
                                score = fitted_model.bic
                            else:
                                score = fitted_model.aic  # Default

                            if score < best_score:
                                best_score = score
                                best_model = fitted_model
                                best_params = (p, d, q)

                        except Exception:
                            continue

            if best_model is not None:
                logger.info(f"✅ Best model found: ARIMA{best_params} with {config.optimization_metric.upper()}: {best_score:.2f}")

            return best_model

        except Exception as e:
            logger.error(f"❌ Manual ARIMA training failed: {e}")
            return None

    def _evaluate_model_performance(self, model: Any, time_series: pd.Series,
                                  business_type: BusinessType, training_time: float) -> ModelPerformance:
        """Evaluate model performance with comprehensive metrics"""
        try:
            # Split data for validation
            split_point = int(len(time_series) * (1 - self.config.validation_split))
            train_data = time_series[:split_point]
            test_data = time_series[split_point:]

            if len(test_data) == 0:
                test_data = time_series[-5:]  # Use last 5 points if no test data

            # Generate predictions for test period
            start_time = time.time()

            if hasattr(model, 'forecast'):
                # statsmodels ARIMA
                predictions = model.forecast(steps=len(test_data))
                model_params = model.model.order
                aic = model.aic
                bic = model.bic
            elif hasattr(model, 'predict'):
                # pmdarima auto_arima
                predictions = model.predict(n_periods=len(test_data))
                model_params = model.order
                aic = model.aic()
                bic = model.bic()
            else:
                raise ValueError("Unsupported model type")

            prediction_time = time.time() - start_time

            # Calculate performance metrics
            mae = float(np.mean(np.abs(test_data - predictions)))
            mse = float(np.mean((test_data - predictions) ** 2))
            rmse = float(np.sqrt(mse))
            mape = float(np.mean(np.abs((test_data - predictions) / test_data)) * 100)

            # Validation score (composite metric)
            validation_score = 0.4 * (1 - min(mape/100, 1)) + 0.3 * (1 - min(rmse/test_data.mean(), 1)) + 0.3 * (1 - min(mae/test_data.mean(), 1))

            # Residual analysis
            residuals = test_data - predictions
            residual_analysis = {
                'mean_residual': float(np.mean(residuals)),
                'std_residual': float(np.std(residuals)),
                'skewness': float(stats.skew(residuals)) if SCIPY_AVAILABLE else 0.0,
                'kurtosis': float(stats.kurtosis(residuals)) if SCIPY_AVAILABLE else 0.0
            }

            return ModelPerformance(
                business_type=business_type,
                model_params=model_params,
                aic=aic,
                bic=bic,
                mae=mae,
                mse=mse,
                rmse=rmse,
                mape=mape,
                training_time=training_time,
                prediction_time=prediction_time,
                validation_score=validation_score,
                residual_analysis=residual_analysis
            )

        except Exception as e:
            logger.error(f"❌ Model evaluation failed: {e}")
            # Return default performance object
            return ModelPerformance(
                business_type=business_type,
                model_params=(1, 1, 1),
                aic=float('inf'),
                bic=float('inf'),
                mae=float('inf'),
                mse=float('inf'),
                rmse=float('inf'),
                mape=100.0,
                training_time=training_time,
                prediction_time=0.0,
                validation_score=0.0
            )

    def get_model_summary(self, business_type: Optional[BusinessType] = None) -> Dict[str, Any]:
        """Get comprehensive model summary"""
        target_business_type = business_type or self.business_type

        if target_business_type not in self.trained_models:
            return {"error": f"No trained model for {target_business_type}"}

        model_info = self.trained_models[target_business_type]
        performance = self.model_performances[target_business_type]

        return {
            "business_type": target_business_type.value,
            "model_params": performance.model_params,
            "performance_metrics": {
                "aic": performance.aic,
                "bic": performance.bic,
                "mae": performance.mae,
                "rmse": performance.rmse,
                "mape": performance.mape,
                "validation_score": performance.validation_score
            },
            "training_info": {
                "training_time": performance.training_time,
                "prediction_time": performance.prediction_time,
                "data_points": model_info['data_points']
            },
            "business_characteristics": {
                "volatility_level": self.business_characteristics.volatility_level,
                "seasonality_patterns": self.business_characteristics.seasonality_patterns,
                "recommended_horizon": self.business_characteristics.recommended_forecast_horizon
            }
        }


def main():
    """
    Main function to demonstrate dynamic ARIMA training
    """
    print("🚀 Dynamic ARIMA Training System")
    print("=" * 50)

    try:
        # Initialize trainer
        trainer = DynamicARIMATrainer()

        # Load sample data
        print("\n📊 Loading sample data...")
        sample_files = [f"sample/part{i}.json" for i in range(1, 6)]  # Use first 5 parts

        all_results = []

        for sample_file in sample_files:
            try:
                print(f"\n🔄 Processing {sample_file}...")

                # Load data
                if not trainer.load_data(sample_file, "json"):
                    print(f"❌ Failed to load {sample_file}")
                    continue

                # Analyze and classify business type
                business_type, confidence = trainer.analyze_and_classify()
                print(f"📊 Business Type: {business_type.value} (confidence: {confidence:.2f})")

                # Preprocess data
                if not trainer.preprocess_data():
                    print(f"❌ Failed to preprocess data for {sample_file}")
                    continue

                # Train model
                if not trainer.train_model():
                    print(f"❌ Failed to train model for {sample_file}")
                    continue

                # Generate forecast
                forecast_result = trainer.forecast(steps=14)  # 2-week forecast

                if forecast_result:
                    print(f"✅ Generated 14-day forecast")
                    print(f"📈 Forecast range: ${min(forecast_result['forecast_values']):.2f} to ${max(forecast_result['forecast_values']):.2f}")

                # Get model summary
                summary = trainer.get_model_summary()

                all_results.append({
                    'file': sample_file,
                    'business_type': business_type.value,
                    'confidence': confidence,
                    'model_summary': summary,
                    'forecast': forecast_result
                })

                print(f"✅ Completed processing {sample_file}")

            except Exception as e:
                print(f"❌ Error processing {sample_file}: {e}")
                continue

        # Summary report
        print("\n" + "=" * 50)
        print("📋 TRAINING SUMMARY REPORT")
        print("=" * 50)

        business_type_counts = {}
        total_accuracy = 0
        successful_trainings = 0

        for result in all_results:
            bt = result['business_type']
            business_type_counts[bt] = business_type_counts.get(bt, 0) + 1

            if 'model_summary' in result and 'performance_metrics' in result['model_summary']:
                mape = result['model_summary']['performance_metrics'].get('mape', 100)
                if mape < 100:  # Valid MAPE
                    total_accuracy += (100 - mape)
                    successful_trainings += 1

        print(f"\n📊 Business Types Detected:")
        for bt, count in business_type_counts.items():
            print(f"   • {bt}: {count} samples")

        if successful_trainings > 0:
            avg_accuracy = total_accuracy / successful_trainings
            print(f"\n🎯 Average Model Accuracy: {avg_accuracy:.1f}%")

        print(f"\n✅ Successfully processed: {len(all_results)}/{len(sample_files)} samples")

        # Save results
        results_file = f"dynamic_arima_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(results_file, 'w') as f:
            json.dump(all_results, f, indent=2, default=str)

        print(f"💾 Results saved to: {results_file}")

        return all_results

    except Exception as e:
        print(f"❌ Main execution failed: {e}")
        return []


if __name__ == "__main__":
    # Check dependencies
    missing_deps = []
    if not STATSMODELS_AVAILABLE:
        missing_deps.append("statsmodels")
    if not AUTO_ARIMA_AVAILABLE:
        missing_deps.append("pmdarima")
    if not SKLEARN_AVAILABLE:
        missing_deps.append("scikit-learn")
    if not SCIPY_AVAILABLE:
        missing_deps.append("scipy")

    if missing_deps:
        print("⚠️ Missing dependencies:")
        for dep in missing_deps:
            print(f"   pip install {dep}")
        print("\nSome features may not be available.")

    # Run main function
    results = main()

    if results:
        print(f"\n🎉 Dynamic ARIMA training completed successfully!")
        print(f"📊 Processed {len(results)} datasets with intelligent business type classification")
        print(f"🤖 Models trained with business-specific optimizations")
        print(f"🔮 Forecasts generated with adaptive horizons")
    else:
        print("\n❌ No successful results. Check data files and dependencies.")
