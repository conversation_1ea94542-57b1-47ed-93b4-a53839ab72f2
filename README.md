# Financial Reports ML System

A complete machine learning system for financial transaction analysis with ARIMA forecasting and web dashboard.

## 🚀 Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Process Data (First Time Setup)
```bash
python process_data.py
```

### 3. Run All Services
```bash
python run_all_services.py
```

### 4. Access the System
- **Web Dashboard**: http://localhost:3000
- **API Service**: http://localhost:8001
- **API Documentation**: http://localhost:8001/docs

## 📁 Project Structure

```
financereports_ml/
├── 📊 Core System
│   ├── arima_api.py              # FastAPI web service
│   ├── arima_forecasting.py      # ARIMA model implementation
│   ├── database_manager.py       # Database operations
│   ├── data_cleaning.py          # Data cleaning pipeline
│   └── simple_json_transformer.py # JSON data transformation
│
├── 🌐 Web Dashboard
│   ├── app/index.html            # Dashboard interface
│   ├── app/styles.css            # Modern styling
│   ├── app/app.js               # Interactive functionality
│   └── app/server.py            # Web server
│
├── 🛠️ Management Scripts
│   ├── run_all_services.py       # Start all services
│   ├── process_data.py           # Data processing pipeline
│   └── requirements.txt          # Dependencies
│
└── 📄 Data Files (Generated)
    ├── transaction_data.csv       # Original data (required)
    ├── cleaned_transaction_data.csv # Cleaned data
    ├── transaction_*.json         # JSON exports
    └── arima_model.pkl            # Trained model
```

## 🎯 Features

### 📈 **ARIMA Forecasting**
- Automatic parameter optimization
- Revenue prediction with confidence intervals
- Model persistence and reloading
- Performance metrics (AIC, BIC)

### 📊 **Interactive Dashboard**
- Real-time statistics display
- Interactive charts and visualizations
- Forecast generation interface
- Model training controls

### 🔧 **RESTful API**
- Health monitoring endpoints
- Database statistics
- Forecast generation
- Model training and management

### 🗄️ **Database Integration**
- MySQL database support
- Batch data loading
- Query optimization
- Transaction management

## 📋 Prerequisites

- **Python 3.7+**
- **MySQL Server** (XAMPP recommended)
- **Transaction Data** (`transaction_data.csv`)

## 🔧 Configuration

### Database Setup
The system uses MySQL with these default settings:
- **Host**: localhost
- **User**: root
- **Password**: (empty)
- **Database**: financereports

### Data Requirements
Place your transaction data in `transaction_data.csv` with columns:
- UserId, TransactionId, TransactionTime
- ItemCode, ItemDescription, NumberOfItemsPurchased
- CostPerItem, Country

## 📊 Usage Examples

### Generate Forecasts
```python
import requests

# Quick 7-day forecast
response = requests.get("http://localhost:8001/forecast/quick?steps=7")
forecast = response.json()
```

### Train New Model
```python
import requests

# Train ARIMA model
response = requests.post("http://localhost:8001/train", json={
    "period": "daily",
    "metric": "revenue",
    "force_retrain": True
})
```

### Get Database Stats
```python
import requests

# Get database statistics
response = requests.get("http://localhost:8001/database-stats")
stats = response.json()
```

## 🛠️ Development

### Adding New Features
1. **API Endpoints**: Add to `arima_api.py`
2. **Data Processing**: Extend `data_cleaning.py`
3. **Models**: Enhance `arima_forecasting.py`
4. **UI Components**: Modify `app/` files

### Testing
The system includes built-in health checks and error handling.

## 🔍 Troubleshooting

### Common Issues

**API Won't Start**
- Check if port 8000 is available
- Verify all dependencies are installed
- Ensure database is running

**No Data Found**
- Run `python process_data.py` first
- Check if `transaction_data.csv` exists
- Verify database connection

**Forecast Generation Fails**
- Ensure ARIMA model is trained
- Check database has sufficient data
- Verify time series data exists

**Web Dashboard Not Loading**
- Check if port 3000 is available
- Verify all app/ files exist
- Check browser console for errors

## 📈 Performance

### System Requirements
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 1GB for data and models
- **CPU**: Multi-core recommended for large datasets

### Optimization Tips
- Use batch processing for large datasets
- Monitor database query performance
- Cache frequently accessed data
- Use appropriate forecast periods

## 🔐 Security

### Production Considerations
- Change default database credentials
- Use HTTPS for API endpoints
- Implement authentication if needed
- Validate all input data

## 📞 Support

### Getting Help
1. Check the troubleshooting section
2. Review API documentation at `/docs`
3. Check log files for error details
4. Verify all prerequisites are met

### System Health
Monitor these endpoints:
- `/health` - API service status
- `/database-stats` - Database connectivity
- `/model/info` - ARIMA model status

---

## 🎉 Success!

Your Financial Reports ML system is now ready to provide powerful insights into financial data with advanced ARIMA forecasting capabilities!

**Next Steps:**
1. Upload your transaction data
2. Run the data processing pipeline
3. Start the services
4. Explore the web dashboard
5. Generate forecasts and analyze trends
