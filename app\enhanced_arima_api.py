"""
Dynamic ARIMA API - Updated with business-specific forecasting
"""

from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import logging
import sys
import os
import pickle
from pathlib import Path

# Add parent directory to path to import dynamic_arima_trainer
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    # Import the dynamic ARIMA system
    from dynamic_arima_trainer import DynamicARIMATrainer, BusinessType
    DYNAMIC_ARIMA_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Dynamic ARIMA not available: {e}")
    DYNAMIC_ARIMA_AVAILABLE = False

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="Dynamic ARIMA Forecasting API", version="3.0.0")

# Global model registry
loaded_models = {}
model_registry = {}
models_loaded = False

def _get_available_models():
    """Get list of available model files"""
    models_dir = Path("../models")
    if not models_dir.exists():
        return []

    model_files = list(models_dir.glob("*.pkl"))
    return [str(f.name) for f in model_files]

def _load_model(model_path: str):
    """Load a single model from pickle file"""
    try:
        with open(model_path, 'rb') as f:
            model_data = pickle.load(f)
        return model_data
    except Exception as e:
        logger.error(f"Failed to load model {model_path}: {e}")
        return None

def _load_all_available_models():
    """Load all available models"""
    global loaded_models, model_registry, models_loaded

    models_dir = Path("../models")
    if not models_dir.exists():
        logger.warning("Models directory not found")
        return False

    model_files = list(models_dir.glob("*.pkl"))
    if not model_files:
        logger.warning("No model files found")
        return False

    loaded_count = 0
    for model_file in model_files:
        model_data = _load_model(str(model_file))
        if model_data:
            # Extract business type from filename or model data
            business_type = model_data.get('business_type', 'unknown')
            loaded_models[business_type] = model_data
            model_registry[business_type] = str(model_file.name)
            loaded_count += 1
            logger.info(f"Loaded model for {business_type}: {model_file.name}")

    models_loaded = loaded_count > 0
    logger.info(f"Loaded {loaded_count} models")
    return models_loaded

class ForecastRequest(BaseModel):
    steps: int = 30
    confidence_level: float = 0.95
    business_type: Optional[str] = None  # Auto-detect if not provided
    data_file: Optional[str] = None  # For custom data

class ForecastResponse(BaseModel):
    forecast: List[float]
    confidence_intervals: List[List[float]]
    dates: List[str]
    business_type: str
    model_info: Dict[str, Any]
    performance_metrics: Dict[str, float]

class TrainingResponse(BaseModel):
    message: str
    models_trained: int
    business_types_detected: List[str]
    model_paths: List[str]

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Dynamic ARIMA Forecasting API v3.0",
        "features": [
            "Automatic business type classification",
            "Business-specific model optimization",
            "Dynamic parameter selection",
            "Multi-business type support",
            "Production-ready implementation"
        ],
        "supported_business_types": [bt.value for bt in BusinessType],
        "status": "ready"
    }

@app.get("/model-status")
async def get_model_status():
    """Get current model status"""
    global loaded_models, model_registry, models_loaded

    if not models_loaded or not loaded_models:
        return {
            "status": "not_loaded",
            "message": "No models loaded yet",
            "models_count": 0,
            "available_models": _get_available_models()
        }

    return {
        "status": "ready",
        "message": "Dynamic ARIMA models ready",
        "models_count": len(loaded_models),
        "loaded_business_types": list(loaded_models.keys()),
        "model_registry": model_registry,
        "available_models": _get_available_models()
    }

@app.post("/load-models")
async def load_models():
    """Load all available dynamic ARIMA models"""
    try:
        success = _load_all_available_models()

        if not success:
            raise HTTPException(status_code=404, detail="No models found to load")

        return {
            "message": "Models loaded successfully",
            "models_loaded": len(loaded_models),
            "business_types": list(loaded_models.keys()),
            "model_files": list(model_registry.values())
        }

    except Exception as e:
        logger.error(f"❌ Model loading failed: {e}")
        raise HTTPException(status_code=500, detail=f"Model loading failed: {str(e)}")

@app.post("/train", response_model=TrainingResponse)
async def train_dynamic_models():
    """Train dynamic ARIMA models on sample data"""
    try:
        if not DYNAMIC_ARIMA_AVAILABLE:
            raise HTTPException(status_code=500, detail="Dynamic ARIMA system not available")

        logger.info("🚀 Starting dynamic ARIMA training...")

        # Run the training script
        import subprocess
        result = subprocess.run(
            ["python", "../train_dynamic_models.py"],
            capture_output=True,
            text=True,
            cwd=os.path.dirname(os.path.abspath(__file__))
        )

        if result.returncode != 0:
            raise HTTPException(status_code=500, detail=f"Training script failed: {result.stderr}")

        # Load the newly trained models
        success = _load_all_available_models()

        if not success:
            raise HTTPException(status_code=500, detail="Training completed but failed to load models")

        logger.info("✅ Dynamic ARIMA training completed successfully")

        return TrainingResponse(
            message="Dynamic ARIMA models trained successfully",
            models_trained=len(loaded_models),
            business_types_detected=list(loaded_models.keys()),
            model_paths=list(model_registry.values())
        )

    except Exception as e:
        logger.error(f"❌ Training failed: {e}")
        raise HTTPException(status_code=500, detail=f"Training failed: {str(e)}")

@app.post("/forecast", response_model=ForecastResponse)
async def generate_dynamic_forecast(request: ForecastRequest):
    """Generate dynamic ARIMA forecast"""
    global loaded_models, models_loaded

    if not models_loaded or not loaded_models:
        raise HTTPException(status_code=400, detail="Models not loaded. Load models first.")

    try:
        logger.info(f"🔮 Generating dynamic forecast for {request.steps} steps...")

        # Determine business type
        business_type = request.business_type
        if not business_type:
            # Use the first available business type if not specified
            business_type = list(loaded_models.keys())[0]
            logger.info(f"Using default business type: {business_type}")

        if business_type not in loaded_models:
            available_types = list(loaded_models.keys())
            raise HTTPException(
                status_code=400,
                detail=f"Business type '{business_type}' not available. Available types: {available_types}"
            )

        # Get the model data
        model_data = loaded_models[business_type]

        # Create a temporary trainer instance to use the loaded model
        trainer = DynamicARIMATrainer()
        trainer.trained_model = model_data['model']
        trainer.business_type = BusinessType(business_type)
        trainer.performance_metrics = model_data.get('performance_metrics', {})

        # Generate forecast
        forecast_result = trainer.forecast(steps=request.steps)

        if not forecast_result:
            raise HTTPException(status_code=500, detail="Forecast generation failed")

        # Format response
        response = ForecastResponse(
            forecast=forecast_result.forecast_values,
            confidence_intervals=forecast_result.confidence_intervals,
            dates=[date.strftime('%Y-%m-%d') for date in forecast_result.forecast_dates],
            business_type=business_type,
            model_info={
                'business_type': business_type,
                'model_params': model_data.get('model_params', {}),
                'confidence_level': request.confidence_level,
                'training_date': model_data.get('training_date', 'unknown')
            },
            performance_metrics=model_data.get('performance_metrics', {})
        )

        logger.info("✅ Dynamic forecast generated successfully")
        return response

    except Exception as e:
        logger.error(f"❌ Forecast generation failed: {e}")
        raise HTTPException(status_code=500, detail=f"Forecast failed: {str(e)}")

@app.get("/forecast/tomorrow")
async def get_tomorrow_forecast(business_type: Optional[str] = None):
    """Get tomorrow's forecast (quick endpoint)"""
    global loaded_models, models_loaded

    if not models_loaded or not loaded_models:
        raise HTTPException(status_code=400, detail="Models not loaded")

    try:
        # Use first available business type if not specified
        if not business_type:
            business_type = list(loaded_models.keys())[0]

        if business_type not in loaded_models:
            available_types = list(loaded_models.keys())
            raise HTTPException(
                status_code=400,
                detail=f"Business type '{business_type}' not available. Available: {available_types}"
            )

        # Get model and generate 1-day forecast
        model_data = loaded_models[business_type]
        trainer = DynamicARIMATrainer()
        trainer.trained_model = model_data['model']
        trainer.business_type = BusinessType(business_type)

        forecast_result = trainer.forecast(steps=1)

        if not forecast_result:
            raise HTTPException(status_code=500, detail="Forecast generation failed")

        return {
            "date": forecast_result.forecast_dates[0].strftime('%Y-%m-%d'),
            "forecast": float(forecast_result.forecast_values[0]),
            "confidence_interval": forecast_result.confidence_intervals[0],
            "business_type": business_type
        }

    except Exception as e:
        logger.error(f"❌ Tomorrow forecast failed: {e}")
        raise HTTPException(status_code=500, detail=f"Tomorrow forecast failed: {str(e)}")

@app.get("/performance")
async def get_performance_metrics():
    """Get detailed performance metrics"""
    global loaded_models, models_loaded

    if not models_loaded or not loaded_models:
        return {"error": "No models loaded"}

    try:
        performance_summary = {}

        for business_type, model_data in loaded_models.items():
            performance_metrics = model_data.get('performance_metrics', {})
            performance_summary[business_type] = {
                'model_params': model_data.get('model_params', {}),
                'training_date': model_data.get('training_date', 'unknown'),
                'performance_metrics': performance_metrics,
                'model_file': model_registry.get(business_type, 'unknown')
            }

        return {
            "models_performance": performance_summary,
            "total_models": len(loaded_models),
            "business_types": list(loaded_models.keys()),
            "api_version": "3.0.0",
            "features": [
                "Dynamic business type classification",
                "Business-specific model optimization",
                "Automatic parameter selection",
                "Multi-business type support",
                "Production-ready implementation"
            ]
        }

    except Exception as e:
        logger.error(f"❌ Performance metrics failed: {e}")
        return {"error": f"Failed to get performance metrics: {str(e)}"}

if __name__ == "__main__":
    import uvicorn
    print("🚀 Starting Dynamic ARIMA API...")
    print("📡 API will be available at: http://localhost:8001")
    print("📚 Documentation at: http://localhost:8001/docs")
    print("💡 Use /load-models to load existing models or /train to train new ones")
    uvicorn.run(app, host="0.0.0.0", port=8001)