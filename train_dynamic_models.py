#!/usr/bin/env python3
"""
Dynamic ARIMA Model Training Script
Trains models on all sample data and saves them for production use
"""

import os
import sys
import json
import logging
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dynamic_arima_trainer import DynamicARIMATrainer, BusinessType

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('model_training.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ModelTrainingManager:
    """Manages the training and saving of dynamic ARIMA models"""
    
    def __init__(self):
        self.trainer = DynamicARIMATrainer()
        self.sample_dir = Path("sample")
        self.models_dir = Path("models")
        self.models_dir.mkdir(exist_ok=True)
        
        # Training results
        self.training_results = {}
        self.model_paths = {}
        
    def discover_sample_files(self) -> List[Path]:
        """Discover all sample JSON files"""
        if not self.sample_dir.exists():
            raise FileNotFoundError(f"Sample directory not found: {self.sample_dir}")
        
        sample_files = list(self.sample_dir.glob("part*.json"))
        if not sample_files:
            raise FileNotFoundError("No sample files found in sample directory")
        
        # Sort files for consistent processing
        sample_files.sort(key=lambda x: int(x.stem.replace('part', '')))
        
        logger.info(f"📁 Found {len(sample_files)} sample files")
        return sample_files
    
    def train_on_sample(self, sample_file: Path) -> Dict[str, Any]:
        """Train model on a single sample file"""
        logger.info(f"🔄 Processing: {sample_file.name}")
        
        try:
            # Load data
            load_success = self.trainer.load_data(str(sample_file))
            if not load_success:
                raise Exception("Failed to load data")

            # Preprocess data
            preprocess_success = self.trainer.preprocess_data()
            if not preprocess_success:
                raise Exception("Failed to preprocess data")

            # Get business type classification
            business_type = self.trainer.business_type
            logger.info(f"🏢 Classified as: {business_type.value}")

            # Train model
            training_success = self.trainer.train_model()
            if not training_success:
                raise Exception("Model training failed")
            
            # Get performance metrics
            performance = self.trainer.model_performances.get(business_type)
            if not performance:
                raise Exception("No performance metrics available")
            
            # Save model
            model_path = self.trainer.save_model(business_type, f"sample_{sample_file.stem}")
            
            result = {
                'sample_file': sample_file.name,
                'business_type': business_type.value,
                'model_path': model_path,
                'performance': {
                    'mape': performance.mape,
                    'rmse': performance.rmse,
                    'aic': performance.aic,
                    'bic': performance.bic,
                    'model_params': performance.model_params
                },
                'training_time': self.trainer.trained_models[business_type]['training_time'],
                'data_points': self.trainer.trained_models[business_type]['data_points'],
                'success': True
            }
            
            logger.info(f"✅ {sample_file.name}: {business_type.value} - MAPE: {performance.mape:.2f}%")
            return result
            
        except Exception as e:
            logger.error(f"❌ Failed to process {sample_file.name}: {e}")
            return {
                'sample_file': sample_file.name,
                'error': str(e),
                'success': False
            }
    
    def train_all_models(self) -> Dict[str, Any]:
        """Train models on all sample files"""
        logger.info("🚀 Starting Dynamic ARIMA Model Training")
        logger.info("=" * 60)
        
        start_time = datetime.now()
        
        try:
            # Discover sample files
            sample_files = self.discover_sample_files()
            
            # Train on each sample
            results = []
            successful_trainings = 0
            business_type_counts = {}
            
            for sample_file in sample_files:
                result = self.train_on_sample(sample_file)
                results.append(result)
                
                if result['success']:
                    successful_trainings += 1
                    business_type = result['business_type']
                    business_type_counts[business_type] = business_type_counts.get(business_type, 0) + 1
                    self.model_paths[sample_file.name] = result['model_path']
            
            # Summary
            training_time = (datetime.now() - start_time).total_seconds()
            
            summary = {
                'timestamp': start_time.isoformat(),
                'total_samples': len(sample_files),
                'successful_trainings': successful_trainings,
                'failed_trainings': len(sample_files) - successful_trainings,
                'business_type_counts': business_type_counts,
                'training_time_seconds': training_time,
                'model_paths': self.model_paths,
                'results': results
            }
            
            # Log summary
            logger.info(f"\n📊 Training Summary:")
            logger.info(f"   Total samples: {len(sample_files)}")
            logger.info(f"   Successful: {successful_trainings}")
            logger.info(f"   Failed: {len(sample_files) - successful_trainings}")
            logger.info(f"   Training time: {training_time:.1f}s")
            
            logger.info(f"\n🏢 Business Types Detected:")
            for bt, count in business_type_counts.items():
                logger.info(f"   • {bt}: {count} samples")
            
            logger.info(f"\n💾 Models Saved:")
            for sample, path in self.model_paths.items():
                logger.info(f"   📁 {sample}: {path}")
            
            # Save training summary
            summary_file = f"training_summary_{start_time.strftime('%Y%m%d_%H%M%S')}.json"
            with open(summary_file, 'w') as f:
                json.dump(summary, f, indent=2, default=str)
            
            logger.info(f"\n💾 Training summary saved: {summary_file}")
            
            return summary
            
        except Exception as e:
            logger.error(f"❌ Training failed: {e}")
            raise
    
    def create_model_registry(self) -> str:
        """Create a registry file with all trained models"""
        registry = {
            'created': datetime.now().isoformat(),
            'models': {}
        }
        
        # Scan models directory
        for model_file in self.models_dir.glob("*.pkl"):
            try:
                # Load model metadata
                model_data = self.trainer.load_model(str(model_file))
                
                registry['models'][model_file.name] = {
                    'path': str(model_file),
                    'business_type': model_data['business_type'].value,
                    'performance': {
                        'mape': model_data['performance'].mape,
                        'rmse': model_data['performance'].rmse,
                        'aic': model_data['performance'].aic,
                        'model_params': model_data['performance'].model_params
                    },
                    'training_time': model_data['training_time'],
                    'data_points': model_data['data_points'],
                    'created': model_data['timestamp'].isoformat() if hasattr(model_data['timestamp'], 'isoformat') else str(model_data['timestamp'])
                }
                
            except Exception as e:
                logger.warning(f"⚠️ Could not load model metadata for {model_file.name}: {e}")
        
        # Save registry
        registry_file = "model_registry.json"
        with open(registry_file, 'w') as f:
            json.dump(registry, f, indent=2)
        
        logger.info(f"📋 Model registry created: {registry_file}")
        return registry_file

def main():
    """Main training function"""
    try:
        # Create training manager
        manager = ModelTrainingManager()
        
        # Train all models
        summary = manager.train_all_models()
        
        # Create model registry
        registry_file = manager.create_model_registry()
        
        # Final success message
        if summary['successful_trainings'] > 0:
            logger.info(f"\n🎉 Training completed successfully!")
            logger.info(f"✅ {summary['successful_trainings']} models trained and saved")
            logger.info(f"📋 Model registry: {registry_file}")
            logger.info(f"🚀 Ready for production use!")
        else:
            logger.error(f"\n❌ No models were successfully trained")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Training script failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
