#!/usr/bin/env python3
"""
Simple web server for the Enhanced ARIMA web interface
"""

import http.server
import socketserver
import os
import webbrowser
import threading
import time

def start_web_server():
    """Start the web server"""
    PORT = 3000
    
    # Change to app directory
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    Handler = http.server.SimpleHTTPRequestHandler
    
    with socketserver.TCPServer(("", PORT), Handler) as httpd:
        print("🌐" + "="*50 + "🌐")
        print("    Enhanced ARIMA Web Interface")
        print("🌐" + "="*50 + "🌐")
        print()
        print(f"✅ Web server running at: http://localhost:{PORT}")
        print(f"✅ Enhanced API running at: http://localhost:8001")
        print()
        print("📋 Available interfaces:")
        print(f"   🌐 Web Dashboard: http://localhost:{PORT}")
        print(f"   📚 API Documentation: http://localhost:8001/docs")
        print(f"   🔧 API Status: http://localhost:8001/model-status")
        print()
        print("⌨️ Press Ctrl+C to stop the web server")
        print()
        
        # Open browser after a short delay
        def open_browser():
            time.sleep(2)
            webbrowser.open(f'http://localhost:{PORT}')
        
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 Web server stopped")
            print("👋 Thank you for using Enhanced ARIMA!")

if __name__ == "__main__":
    start_web_server()
