@echo off
echo ========================================
echo    Dynamic ARIMA Training System
echo ========================================
echo.

echo Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again
    pause
    exit /b 1
)

echo Python found. Checking dependencies...

echo Installing/updating required packages...
pip install pandas numpy statsmodels pmdarima scikit-learn scipy --quiet --upgrade

echo.
echo ========================================
echo Starting Dynamic ARIMA Training System
echo ========================================
echo.

echo Choose an option:
echo 1. Run comprehensive test suite
echo 2. Run dynamic ARIMA trainer on sample data
echo 3. Interactive training mode
echo.

set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" (
    echo.
    echo Running comprehensive test suite...
    python test_dynamic_arima.py
) else if "%choice%"=="2" (
    echo.
    echo Running dynamic ARIMA trainer...
    python dynamic_arima_trainer.py
) else if "%choice%"=="3" (
    echo.
    echo Starting interactive training mode...
    python -c "from dynamic_arima_trainer import DynamicARIMATrainer; trainer = DynamicARIMATrainer(); print('Dynamic ARIMA Trainer initialized. Use trainer object for interactive training.')"
) else (
    echo Invalid choice. Running default test suite...
    python test_dynamic_arima.py
)

echo.
echo ========================================
echo Dynamic ARIMA Training Completed
echo ========================================
echo.
echo Check the generated files:
echo - dynamic_arima_results_*.json (training results)
echo - dynamic_arima_test_results_*.json (test results)
echo - dynamic_arima_trainer.log (detailed logs)
echo.

pause
