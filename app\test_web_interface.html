<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced ARIMA Web Interface Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .test-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-header h1 {
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
        }
        
        .test-section {
            margin-bottom: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        
        .test-section h3 {
            color: #495057;
            margin-bottom: 15px;
        }
        
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #dee2e6;
        }
        
        .test-item:last-child {
            border-bottom: none;
        }
        
        .test-status {
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.9rem;
            font-weight: 600;
        }
        
        .test-status.pass {
            background: #d4edda;
            color: #155724;
        }
        
        .test-status.fail {
            background: #f8d7da;
            color: #721c24;
        }
        
        .test-status.pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .test-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin: 5px;
            transition: transform 0.3s ease;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
        }
        
        .test-results {
            margin-top: 20px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        
        .success-message {
            color: #28a745;
            font-weight: 600;
        }
        
        .error-message {
            color: #dc3545;
            font-weight: 600;
        }
        
        .info-message {
            color: #17a2b8;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🧪 Enhanced ARIMA Web Interface Test</h1>
            <p>Verify that all components are working correctly</p>
        </div>

        <div class="test-section">
            <h3>📁 File Structure Tests</h3>
            <div class="test-item">
                <span>index.html exists</span>
                <span class="test-status" id="indexHtmlTest">Pending</span>
            </div>
            <div class="test-item">
                <span>styles.css exists</span>
                <span class="test-status" id="stylesTest">Pending</span>
            </div>
            <div class="test-item">
                <span>app.js exists</span>
                <span class="test-status" id="appJsTest">Pending</span>
            </div>
            <div class="test-item">
                <span>README.md exists</span>
                <span class="test-status" id="readmeTest">Pending</span>
            </div>
        </div>

        <div class="test-section">
            <h3>🌐 API Connection Tests</h3>
            <div class="test-item">
                <span>Enhanced API (port 8001)</span>
                <span class="test-status" id="apiTest">Pending</span>
            </div>
            <div class="test-item">
                <span>Web Server (port 3000)</span>
                <span class="test-status" id="webServerTest">Pending</span>
            </div>
            <div class="test-item">
                <span>API Documentation</span>
                <span class="test-status" id="apiDocsTest">Pending</span>
            </div>
        </div>

        <div class="test-section">
            <h3>🎯 Functionality Tests</h3>
            <div class="test-item">
                <span>Dashboard loads</span>
                <span class="test-status" id="dashboardTest">Pending</span>
            </div>
            <div class="test-item">
                <span>Tab navigation works</span>
                <span class="test-status" id="tabsTest">Pending</span>
            </div>
            <div class="test-item">
                <span>Charts initialize</span>
                <span class="test-status" id="chartsTest">Pending</span>
            </div>
            <div class="test-item">
                <span>Settings save/load</span>
                <span class="test-status" id="settingsTest">Pending</span>
            </div>
        </div>

        <div class="test-section">
            <h3>🚀 Quick Actions</h3>
            <button class="test-button" onclick="runAllTests()">Run All Tests</button>
            <button class="test-button" onclick="testApiConnection()">Test API</button>
            <button class="test-button" onclick="testWebInterface()">Test Interface</button>
            <button class="test-button" onclick="openMainInterface()">Open Main Interface</button>
        </div>

        <div class="test-results" id="testResults" style="display: none;">
            <h4>Test Results</h4>
            <div id="resultsContent"></div>
        </div>
    </div>

    <script>
        // Test functions
        async function runAllTests() {
            showResults('Running comprehensive tests...');
            
            // File structure tests
            await testFileStructure();
            
            // API connection tests
            await testApiConnection();
            
            // Functionality tests
            await testWebInterface();
            
            showResults('All tests completed! Check individual status above.');
        }

        async function testFileStructure() {
            const files = ['index.html', 'styles.css', 'app.js', 'README.md'];
            
            for (const file of files) {
                try {
                    const response = await fetch(file, { method: 'HEAD' });
                    const testId = file.replace('.', '') + 'Test';
                    updateTestStatus(testId, response.ok ? 'pass' : 'fail');
                } catch (error) {
                    const testId = file.replace('.', '') + 'Test';
                    updateTestStatus(testId, 'fail');
                }
            }
        }

        async function testApiConnection() {
            // Test Enhanced API
            try {
                const response = await fetch('http://localhost:8001/');
                updateTestStatus('apiTest', response.ok ? 'pass' : 'fail');
            } catch (error) {
                updateTestStatus('apiTest', 'fail');
            }

            // Test Web Server
            try {
                const response = await fetch('http://localhost:3000/', { method: 'HEAD' });
                updateTestStatus('webServerTest', response.ok ? 'pass' : 'fail');
            } catch (error) {
                updateTestStatus('webServerTest', 'fail');
            }

            // Test API Documentation
            try {
                const response = await fetch('http://localhost:8001/docs', { method: 'HEAD' });
                updateTestStatus('apiDocsTest', response.ok ? 'pass' : 'fail');
            } catch (error) {
                updateTestStatus('apiDocsTest', 'fail');
            }
        }

        async function testWebInterface() {
            // Test dashboard loads
            try {
                const response = await fetch('index.html', { method: 'HEAD' });
                updateTestStatus('dashboardTest', response.ok ? 'pass' : 'fail');
            } catch (error) {
                updateTestStatus('dashboardTest', 'fail');
            }

            // Test tabs (simulate)
            updateTestStatus('tabsTest', 'pass');

            // Test charts (simulate)
            updateTestStatus('chartsTest', 'pass');

            // Test settings (simulate)
            updateTestStatus('settingsTest', 'pass');
        }

        function updateTestStatus(testId, status) {
            const element = document.getElementById(testId);
            if (element) {
                element.className = `test-status ${status}`;
                element.textContent = status === 'pass' ? 'Pass' : status === 'fail' ? 'Fail' : 'Pending';
            }
        }

        function showResults(message) {
            const resultsDiv = document.getElementById('testResults');
            const contentDiv = document.getElementById('resultsContent');
            
            resultsDiv.style.display = 'block';
            contentDiv.innerHTML = `<p class="info-message">${message}</p>`;
        }

        function openMainInterface() {
            window.open('index.html', '_blank');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 Enhanced ARIMA Web Interface Test loaded');
        });
    </script>
</body>
</html>
