#!/usr/bin/env python3
"""
Quick Start Script for ARIMA Forecasting System
Run this script to test the forecasting system with your data
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

from datetime import datetime, timedelta
import json
import os

# Check if required packages are available
try:
    from statsmodels.tsa.arima.model import ARIMA
    from sklearn.metrics import mean_absolute_error, mean_squared_error
    print("✅ All required packages are available!")
except ImportError as e:
    print(f"❌ Missing package: {e}")
    print("Please install with: pip install statsmodels scikit-learn")
    exit(1)

class QuickARIMAForecaster:
    """
    Simplified ARIMA forecaster for quick testing
    """

    def __init__(self):
        self.time_series = None
        self.model = None
        self.fitted_model = None
        self.model_params = None

    def load_sample_data(self, sample_file='sample/part1.json'):
        """Load data from sample JSON file"""
        print(f"📊 Loading sample data from {sample_file}...")

        try:
            with open(sample_file, 'r') as f:
                data = json.load(f)

            # Extract transactions
            transactions = data['transactions']
            df = pd.DataFrame(transactions)

            # Convert datetime
            df['transactiontime'] = pd.to_datetime(df['transactiontime'])

            # Create daily revenue time series
            df['date'] = df['transactiontime'].dt.date
            daily_revenue = df.groupby('date')['total_transaction_value'].sum()
            daily_revenue.index = pd.to_datetime(daily_revenue.index)

            self.time_series = daily_revenue.sort_index()

            print(f"✅ Loaded {len(self.time_series)} daily data points")
            print(f"📅 Date range: {self.time_series.index[0]} to {self.time_series.index[-1]}")
            print(f"💰 Total revenue: ${self.time_series.sum():,.2f}")
            print(f"💰 Average daily revenue: ${self.time_series.mean():,.2f}")

            return self.time_series

        except Exception as e:
            print(f"❌ Error loading data: {e}")
            return None

    def load_full_data(self, csv_file='cleaned_transaction_data.csv', metric='revenue'):
        """Load data from CSV file"""
        print(f"📊 Loading full dataset from {csv_file}...")

        try:
            df = pd.read_csv(csv_file)
            df['transactiontime'] = pd.to_datetime(df['transactiontime'])

            # Create daily time series
            df['date'] = df['transactiontime'].dt.date

            if metric == 'revenue':
                daily_data = df.groupby('date')['total_transaction_value'].sum()
            elif metric == 'transaction_count':
                daily_data = df.groupby('date').size()
            elif metric == 'quantity':
                daily_data = df.groupby('date')['numberofitemspurchased'].sum()

            daily_data.index = pd.to_datetime(daily_data.index)
            self.time_series = daily_data.sort_index()

            print(f"✅ Loaded {len(self.time_series)} daily data points")
            print(f"📅 Date range: {self.time_series.index[0]} to {self.time_series.index[-1]}")

            if metric == 'revenue':
                print(f"💰 Total revenue: ${self.time_series.sum():,.2f}")
                print(f"💰 Average daily revenue: ${self.time_series.mean():,.2f}")
            else:
                print(f"📊 Total {metric}: {self.time_series.sum():,.0f}")
                print(f"📊 Average daily {metric}: {self.time_series.mean():,.0f}")

            return self.time_series

        except Exception as e:
            print(f"❌ Error loading data: {e}")
            return None

    def find_best_params(self, max_p=2, max_q=2):
        """Find best ARIMA parameters"""
        print("🔍 Finding optimal ARIMA parameters...")

        best_aic = float('inf')
        best_params = (1, 1, 1)

        for p in range(max_p + 1):
            for d in range(2):
                for q in range(max_q + 1):
                    try:
                        model = ARIMA(self.time_series, order=(p, d, q))
                        fitted = model.fit()

                        if fitted.aic < best_aic:
                            best_aic = fitted.aic
                            best_params = (p, d, q)

                    except:
                        continue

        print(f"✅ Best parameters: ARIMA{best_params} (AIC: {best_aic:.2f})")
        return best_params

    def train_model(self, order=None):
        """Train ARIMA model"""
        if self.time_series is None:
            print("❌ No data loaded. Please load data first.")
            return False

        print("🤖 Training ARIMA model...")

        try:
            if order is None:
                order = self.find_best_params()

            self.model_params = order
            self.model = ARIMA(self.time_series, order=order)
            self.fitted_model = self.model.fit()

            print(f"✅ Model trained successfully!")
            print(f"📊 ARIMA{order} - AIC: {self.fitted_model.aic:.2f}")

            return True

        except Exception as e:
            print(f"❌ Training failed: {e}")
            return False

    def validate_model(self, test_size=0.3):
        """Validate model performance"""
        if self.fitted_model is None:
            print("❌ Model not trained yet.")
            return None

        print("🧪 Validating model...")

        # Split data
        split_point = int(len(self.time_series) * (1 - test_size))
        train_data = self.time_series[:split_point]
        test_data = self.time_series[split_point:]

        # Retrain on training data
        model = ARIMA(train_data, order=self.model_params)
        fitted_model = model.fit()

        # Make predictions
        forecast = fitted_model.forecast(steps=len(test_data))

        # Calculate metrics
        mae = mean_absolute_error(test_data, forecast)
        mse = mean_squared_error(test_data, forecast)
        rmse = np.sqrt(mse)
        mape = np.mean(np.abs((test_data - forecast) / test_data)) * 100
        accuracy = max(0, 100 - mape)

        print(f"✅ Validation Results:")
        print(f"📊 Accuracy: {accuracy:.1f}%")
        print(f"📊 MAPE: {mape:.2f}%")
        print(f"📊 RMSE: {rmse:.2f}")
        print(f"📊 MAE: {mae:.2f}")

        return {
            'accuracy': accuracy,
            'mape': mape,
            'rmse': rmse,
            'mae': mae
        }

    def forecast(self, days=30):
        """Generate forecast"""
        if self.fitted_model is None:
            print("❌ Model not trained yet.")
            return None

        print(f"🔮 Generating {days}-day forecast...")

        try:
            # Generate forecast
            forecast_result = self.fitted_model.get_forecast(steps=days)
            forecast_values = forecast_result.predicted_mean
            conf_int = forecast_result.conf_int()

            # Create future dates
            last_date = self.time_series.index[-1]
            future_dates = pd.date_range(start=last_date + timedelta(days=1), periods=days, freq='D')

            # Create forecast dataframe
            forecast_df = pd.DataFrame({
                'date': future_dates,
                'forecast': forecast_values.values,
                'lower_ci': conf_int.iloc[:, 0].values,
                'upper_ci': conf_int.iloc[:, 1].values
            })

            print(f"✅ Forecast generated!")
            print(f"💰 Total forecasted value: ${forecast_values.sum():,.2f}")
            print(f"💰 Average daily forecast: ${forecast_values.mean():,.2f}")

            return forecast_df

        except Exception as e:
            print(f"❌ Forecast failed: {e}")
            return None

    def plot_results(self, forecast_df=None):
        """Plot historical data and forecast"""
        plt.figure(figsize=(12, 6))

        # Plot historical data
        plt.plot(self.time_series.index, self.time_series.values,
                label='Historical Data', color='blue', linewidth=2)

        if forecast_df is not None:
            # Plot forecast
            plt.plot(forecast_df['date'], forecast_df['forecast'],
                    label='Forecast', color='red', linewidth=2, linestyle='--')

            # Plot confidence interval
            plt.fill_between(forecast_df['date'],
                           forecast_df['lower_ci'],
                           forecast_df['upper_ci'],
                           alpha=0.3, color='red', label='95% Confidence Interval')

        plt.title('ARIMA Forecast Results')
        plt.xlabel('Date')
        plt.ylabel('Value')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.show()

def main():
    """Main execution function"""
    print("🚀 ARIMA Forecasting System - Quick Start")
    print("=" * 50)

    # Initialize forecaster
    forecaster = QuickARIMAForecaster()

    # Try to load data (sample first, then full dataset)
    data_loaded = False

    # Try sample data first
    if os.path.exists('sample/part1.json'):
        print("\n📦 Found sample data, loading...")
        result = forecaster.load_sample_data()
        if result is not None:
            data_loaded = True

    # Try full dataset if sample not available
    if not data_loaded and os.path.exists('cleaned_transaction_data.csv'):
        print("\n📊 Loading full dataset...")
        result = forecaster.load_full_data()
        if result is not None:
            data_loaded = True

    if not data_loaded:
        print("❌ No data files found!")
        print("Please ensure you have either:")
        print("  - sample/part1.json (sample data)")
        print("  - cleaned_transaction_data.csv (full dataset)")
        return

    # Train model
    print("\n🤖 Training ARIMA model...")
    if not forecaster.train_model():
        print("❌ Training failed!")
        return

    # Validate model
    print("\n🧪 Validating model...")
    validation_results = forecaster.validate_model()

    # Generate forecast
    print("\n🔮 Generating forecast...")
    forecast_df = forecaster.forecast(days=30)

    if forecast_df is not None:
        print("\n📈 Forecast Preview:")
        print(forecast_df.head(10).to_string(index=False))

        # Plot results
        print("\n📊 Generating plot...")
        try:
            forecaster.plot_results(forecast_df)
        except Exception as e:
            print(f"⚠️ Plotting failed: {e}")
            print("(This is normal in some environments)")

    print("\n🎉 ARIMA forecasting completed successfully!")
    print("\n💡 Next steps:")
    print("  - Try different forecast horizons")
    print("  - Test with different metrics (revenue, transaction_count, quantity)")
    print("  - Run the full Jupyter notebook for advanced features")

if __name__ == "__main__":
    main()
