"""
Data Cleaning Script for Financial Transaction Data
This script performs comprehensive data cleaning on transaction_data.csv
"""

import pandas as pd
import numpy as np
import re
from datetime import datetime
import logging
from typing import Tuple, Dict, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('data_cleaning.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DataCleaner:
    """Class to handle data cleaning operations for transaction data"""
    
    def __init__(self, file_path: str):
        self.file_path = file_path
        self.df = None
        self.original_shape = None
        self.cleaning_report = {}
    
    def load_data(self) -> pd.DataFrame:
        """Load the CSV data and perform initial inspection"""
        logger.info(f"Loading data from {self.file_path}")
        try:
            self.df = pd.read_csv(self.file_path)
            self.original_shape = self.df.shape
            logger.info(f"Data loaded successfully. Shape: {self.original_shape}")
            return self.df
        except Exception as e:
            logger.error(f"Error loading data: {e}")
            raise
    
    def inspect_data(self) -> Dict[str, Any]:
        """Perform initial data inspection"""
        logger.info("Performing initial data inspection...")
        
        inspection = {
            'shape': self.df.shape,
            'columns': list(self.df.columns),
            'dtypes': self.df.dtypes.to_dict(),
            'missing_values': self.df.isnull().sum().to_dict(),
            'duplicates': self.df.duplicated().sum(),
            'memory_usage': self.df.memory_usage(deep=True).sum()
        }
        
        # Sample data
        inspection['sample_data'] = self.df.head().to_dict()
        
        logger.info(f"Data inspection completed. Found {inspection['duplicates']} duplicates")
        return inspection
    
    def clean_column_names(self) -> None:
        """Standardize column names"""
        logger.info("Cleaning column names...")
        original_columns = self.df.columns.tolist()
        
        # Convert to snake_case and remove special characters
        new_columns = []
        for col in self.df.columns:
            # Convert to lowercase and replace spaces/special chars with underscores
            clean_col = re.sub(r'[^a-zA-Z0-9]', '_', col.lower())
            clean_col = re.sub(r'_+', '_', clean_col).strip('_')
            new_columns.append(clean_col)
        
        self.df.columns = new_columns
        logger.info(f"Column names updated: {dict(zip(original_columns, new_columns))}")
    
    def handle_missing_values(self) -> None:
        """Handle missing values in the dataset"""
        logger.info("Handling missing values...")
        
        missing_before = self.df.isnull().sum().sum()
        
        # Strategy for different columns
        strategies = {
            'userid': 'drop',  # Critical field
            'transactionid': 'drop',  # Critical field
            'transactiontime': 'drop',  # Critical field
            'itemcode': 'drop',  # Critical field
            'itemdescription': 'fill_unknown',
            'numberofitemspurchased': 'fill_median',
            'costperitem': 'fill_median',
            'country': 'fill_unknown'
        }
        
        for column, strategy in strategies.items():
            if column in self.df.columns:
                if strategy == 'drop':
                    before_count = len(self.df)
                    self.df = self.df.dropna(subset=[column])
                    after_count = len(self.df)
                    logger.info(f"Dropped {before_count - after_count} rows due to missing {column}")
                
                elif strategy == 'fill_unknown':
                    self.df[column] = self.df[column].fillna('Unknown')
                
                elif strategy == 'fill_median':
                    if self.df[column].dtype in ['int64', 'float64']:
                        median_val = self.df[column].median()
                        self.df[column] = self.df[column].fillna(median_val)
        
        missing_after = self.df.isnull().sum().sum()
        logger.info(f"Missing values reduced from {missing_before} to {missing_after}")
    
    def clean_data_types(self) -> None:
        """Convert columns to appropriate data types"""
        logger.info("Converting data types...")
        
        try:
            # Convert numeric columns
            if 'userid' in self.df.columns:
                self.df['userid'] = pd.to_numeric(self.df['userid'], errors='coerce')
            
            if 'transactionid' in self.df.columns:
                self.df['transactionid'] = pd.to_numeric(self.df['transactionid'], errors='coerce')
            
            if 'numberofitemspurchased' in self.df.columns:
                self.df['numberofitemspurchased'] = pd.to_numeric(self.df['numberofitemspurchased'], errors='coerce')
            
            if 'costperitem' in self.df.columns:
                self.df['costperitem'] = pd.to_numeric(self.df['costperitem'], errors='coerce')
            
            # Convert datetime
            if 'transactiontime' in self.df.columns:
                self.df['transactiontime'] = pd.to_datetime(self.df['transactiontime'], errors='coerce')
            
            # Convert categorical columns
            categorical_columns = ['country', 'itemdescription']
            for col in categorical_columns:
                if col in self.df.columns:
                    self.df[col] = self.df[col].astype('category')
            
            logger.info("Data type conversion completed")
            
        except Exception as e:
            logger.error(f"Error in data type conversion: {e}")
    
    def remove_duplicates(self) -> None:
        """Remove duplicate records"""
        logger.info("Removing duplicates...")
        
        before_count = len(self.df)
        
        # Remove exact duplicates
        self.df = self.df.drop_duplicates()
        
        # Remove duplicates based on key columns (if they exist)
        key_columns = ['userid', 'transactionid', 'transactiontime']
        existing_key_columns = [col for col in key_columns if col in self.df.columns]
        
        if existing_key_columns:
            self.df = self.df.drop_duplicates(subset=existing_key_columns, keep='first')
        
        after_count = len(self.df)
        duplicates_removed = before_count - after_count
        
        logger.info(f"Removed {duplicates_removed} duplicate records")
        self.cleaning_report['duplicates_removed'] = duplicates_removed
    
    def clean_text_fields(self) -> None:
        """Clean and standardize text fields"""
        logger.info("Cleaning text fields...")
        
        text_columns = ['itemdescription', 'country']
        
        for col in text_columns:
            if col in self.df.columns:
                # Remove extra whitespace
                self.df[col] = self.df[col].astype(str).str.strip()
                
                # Standardize case for country names
                if col == 'country':
                    self.df[col] = self.df[col].str.title()
                
                # Remove special characters from item descriptions
                if col == 'itemdescription':
                    self.df[col] = self.df[col].str.replace(r'[^\w\s]', '', regex=True)
                    self.df[col] = self.df[col].str.upper()
        
        logger.info("Text field cleaning completed")
    
    def validate_numerical_data(self) -> None:
        """Validate and clean numerical data"""
        logger.info("Validating numerical data...")
        
        # Remove negative quantities and costs (assuming they're errors)
        if 'numberofitemspurchased' in self.df.columns:
            before_count = len(self.df)
            self.df = self.df[self.df['numberofitemspurchased'] > 0]
            after_count = len(self.df)
            logger.info(f"Removed {before_count - after_count} records with invalid quantities")
        
        if 'costperitem' in self.df.columns:
            before_count = len(self.df)
            self.df = self.df[self.df['costperitem'] > 0]
            after_count = len(self.df)
            logger.info(f"Removed {before_count - after_count} records with invalid costs")
        
        # Remove outliers (values beyond 3 standard deviations)
        numerical_columns = ['numberofitemspurchased', 'costperitem']
        for col in numerical_columns:
            if col in self.df.columns:
                mean = self.df[col].mean()
                std = self.df[col].std()
                before_count = len(self.df)
                self.df = self.df[abs(self.df[col] - mean) <= 3 * std]
                after_count = len(self.df)
                logger.info(f"Removed {before_count - after_count} outliers from {col}")
    
    def add_derived_features(self) -> None:
        """Add useful derived features"""
        logger.info("Adding derived features...")
        
        # Calculate total transaction value
        if 'numberofitemspurchased' in self.df.columns and 'costperitem' in self.df.columns:
            self.df['total_transaction_value'] = self.df['numberofitemspurchased'] * self.df['costperitem']
        
        # Extract date components
        if 'transactiontime' in self.df.columns:
            self.df['transaction_year'] = self.df['transactiontime'].dt.year
            self.df['transaction_month'] = self.df['transactiontime'].dt.month
            self.df['transaction_day'] = self.df['transactiontime'].dt.day
            self.df['transaction_weekday'] = self.df['transactiontime'].dt.dayofweek
            self.df['transaction_hour'] = self.df['transactiontime'].dt.hour
        
        logger.info("Derived features added successfully")
    
    def generate_cleaning_report(self) -> Dict[str, Any]:
        """Generate a comprehensive cleaning report"""
        logger.info("Generating cleaning report...")
        
        final_shape = self.df.shape
        
        report = {
            'original_shape': self.original_shape,
            'final_shape': final_shape,
            'rows_removed': self.original_shape[0] - final_shape[0],
            'columns_added': final_shape[1] - self.original_shape[1],
            'final_dtypes': self.df.dtypes.to_dict(),
            'final_missing_values': self.df.isnull().sum().to_dict(),
            'memory_usage_mb': self.df.memory_usage(deep=True).sum() / 1024 / 1024,
            'unique_countries': self.df['country'].nunique() if 'country' in self.df.columns else 0,
            'date_range': {
                'start': self.df['transactiontime'].min() if 'transactiontime' in self.df.columns else None,
                'end': self.df['transactiontime'].max() if 'transactiontime' in self.df.columns else None
            }
        }
        
        self.cleaning_report.update(report)
        return self.cleaning_report
    
    def save_cleaned_data(self, output_path: str = 'cleaned_transaction_data.csv') -> None:
        """Save the cleaned dataset"""
        logger.info(f"Saving cleaned data to {output_path}")
        
        try:
            self.df.to_csv(output_path, index=False)
            logger.info(f"Cleaned data saved successfully to {output_path}")
        except Exception as e:
            logger.error(f"Error saving cleaned data: {e}")
            raise
    
    def run_full_cleaning_pipeline(self) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """Run the complete data cleaning pipeline"""
        logger.info("Starting full data cleaning pipeline...")
        
        # Load data
        self.load_data()
        
        # Initial inspection
        initial_inspection = self.inspect_data()
        
        # Cleaning steps
        self.clean_column_names()
        self.handle_missing_values()
        self.clean_data_types()
        self.remove_duplicates()
        self.clean_text_fields()
        self.validate_numerical_data()
        self.add_derived_features()
        
        # Generate final report
        final_report = self.generate_cleaning_report()
        
        # Save cleaned data
        self.save_cleaned_data()
        
        logger.info("Data cleaning pipeline completed successfully!")
        
        return self.df, final_report

def main():
    """Main function to run the data cleaning process"""
    
    # Initialize the data cleaner
    cleaner = DataCleaner('transaction_data.csv')
    
    try:
        # Run the full cleaning pipeline
        cleaned_df, report = cleaner.run_full_cleaning_pipeline()
        
        # Print summary report
        print("\n" + "="*50)
        print("DATA CLEANING SUMMARY REPORT")
        print("="*50)
        print(f"Original shape: {report['original_shape']}")
        print(f"Final shape: {report['final_shape']}")
        print(f"Rows removed: {report['rows_removed']}")
        print(f"Columns added: {report['columns_added']}")
        print(f"Memory usage: {report['memory_usage_mb']:.2f} MB")
        print(f"Unique countries: {report['unique_countries']}")
        
        if report['date_range']['start']:
            print(f"Date range: {report['date_range']['start']} to {report['date_range']['end']}")
        
        print("\nFinal data types:")
        for col, dtype in report['final_dtypes'].items():
            print(f"  {col}: {dtype}")
        
        print("\nCleaned data preview:")
        print(cleaned_df.head())
        
    except Exception as e:
        logger.error(f"Error in main execution: {e}")
        raise

if __name__ == "__main__":
    main()
