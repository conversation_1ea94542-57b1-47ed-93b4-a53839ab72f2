"""
Financial Reports ML - Data Processing Pipeline
This script handles the complete data processing workflow
"""

import os
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_data_cleaning():
    """Run data cleaning process"""
    logger.info("🧹 Starting data cleaning...")
    
    try:
        from data_cleaning import DataCleaner
        
        if not os.path.exists('transaction_data.csv'):
            logger.error("❌ transaction_data.csv not found!")
            return False
        
        # Initialize and run data cleaner
        cleaner = DataCleaner('transaction_data.csv')
        cleaned_df, report = cleaner.run_full_cleaning_pipeline()
        
        logger.info(f"✅ Data cleaning completed: {report['final_shape'][0]} records")
        return True
        
    except Exception as e:
        logger.error(f"❌ Data cleaning failed: {e}")
        return False

def run_json_transformation():
    """Run JSON transformation process"""
    logger.info("📄 Starting JSON transformation...")
    
    try:
        from simple_json_transformer import main as transform_main
        
        if not os.path.exists('cleaned_transaction_data.csv'):
            logger.error("❌ cleaned_transaction_data.csv not found! Run data cleaning first.")
            return False
        
        # Run JSON transformation
        transform_main()
        
        # Check if JSON files were created
        json_files = [
            'transaction_records.json',
            'transaction_aggregated.json', 
            'transaction_timeseries.json'
        ]
        
        created_files = []
        for file in json_files:
            if os.path.exists(file):
                size = os.path.getsize(file) / 1024  # KB
                logger.info(f"✅ Created {file} ({size:.1f} KB)")
                created_files.append(file)
        
        if len(created_files) == len(json_files):
            logger.info("✅ JSON transformation completed successfully")
            return True
        else:
            logger.warning("⚠️ Some JSON files were not created")
            return False
        
    except Exception as e:
        logger.error(f"❌ JSON transformation failed: {e}")
        return False

def run_database_loading():
    """Run database loading process"""
    logger.info("🗄️ Starting database loading...")
    
    try:
        from database_manager import DatabaseManager
        
        # Check if JSON files exist
        json_files = [
            'transaction_records.json',
            'transaction_aggregated.json',
            'transaction_timeseries.json'
        ]
        
        missing_files = [f for f in json_files if not os.path.exists(f)]
        if missing_files:
            logger.error(f"❌ Missing JSON files: {missing_files}")
            return False
        
        # Initialize database manager
        db_manager = DatabaseManager()
        
        # Create database and tables
        if not db_manager.create_database_if_not_exists():
            logger.error("❌ Failed to create database")
            return False
        
        if not db_manager.connect_to_database():
            logger.error("❌ Failed to connect to database")
            return False
        
        if not db_manager.create_tables():
            logger.error("❌ Failed to create tables")
            return False
        
        # Load transaction records in batches
        logger.info("Loading transaction records...")
        success = load_transactions_batch(db_manager)
        
        if success:
            # Get final summary
            summary = db_manager.get_transaction_summary()
            if summary:
                logger.info(f"✅ Database loading completed: {summary['total_transactions']} records")
            
            db_manager.close_connection()
            return True
        else:
            db_manager.close_connection()
            return False
        
    except Exception as e:
        logger.error(f"❌ Database loading failed: {e}")
        return False

def load_transactions_batch(db_manager, batch_size=500):
    """Load transaction data in batches"""
    import json
    import pandas as pd
    import time
    
    try:
        # Load JSON data
        with open('transaction_records.json', 'r', encoding='utf-8') as f:
            json_data = json.load(f)
        
        transactions = json_data.get('transactions', [])
        logger.info(f"Loading {len(transactions)} transactions in batches of {batch_size}")
        
        # Clear existing data
        from sqlalchemy import text
        with db_manager.engine.connect() as conn:
            conn.execute(text("DELETE FROM transactions"))
            conn.commit()
        
        # Process in batches
        total_loaded = 0
        for i in range(0, len(transactions), batch_size):
            batch = transactions[i:i + batch_size]
            
            # Convert to DataFrame
            df_batch = pd.DataFrame(batch)
            
            # Convert datetime
            if 'transactiontime' in df_batch.columns:
                df_batch['transactiontime'] = pd.to_datetime(df_batch['transactiontime'])
            
            # Load to database
            df_batch.to_sql('transactions', db_manager.engine, if_exists='append', index=False)
            total_loaded += len(df_batch)
            
            logger.info(f"Loaded batch {i//batch_size + 1}: {total_loaded}/{len(transactions)} records")
            time.sleep(0.1)  # Small delay
        
        logger.info(f"✅ Successfully loaded {total_loaded} records")
        return True
        
    except Exception as e:
        logger.error(f"❌ Batch loading failed: {e}")
        return False

def train_arima_model():
    """Train ARIMA model"""
    logger.info("🤖 Training ARIMA model...")
    
    try:
        from arima_forecasting import ARIMAForecaster
        
        # Initialize forecaster
        forecaster = ARIMAForecaster()
        
        # Fetch time series data
        df = forecaster.fetch_time_series_data(period='daily', metric='revenue')
        
        if df.empty:
            logger.error("❌ No time series data found in database")
            return False
        
        # Train model
        success = forecaster.train_model()
        
        if success:
            # Save model
            forecaster.save_model()
            logger.info(f"✅ ARIMA model trained successfully: {forecaster.model_params}")
            return True
        else:
            logger.error("❌ ARIMA model training failed")
            return False
        
    except Exception as e:
        logger.error(f"❌ ARIMA training failed: {e}")
        return False

def main():
    """Main data processing pipeline"""
    print("="*70)
    print(" FINANCIAL REPORTS ML - DATA PROCESSING PIPELINE")
    print("="*70)
    print("This script processes data for the Financial Reports ML system")
    print()
    
    # Check what needs to be done
    steps_needed = []
    
    if not os.path.exists('cleaned_transaction_data.csv'):
        steps_needed.append("Data Cleaning")
    
    json_files = ['transaction_records.json', 'transaction_aggregated.json', 'transaction_timeseries.json']
    if not all(os.path.exists(f) for f in json_files):
        steps_needed.append("JSON Transformation")
    
    # Check database
    try:
        from database_manager import DatabaseManager
        db_manager = DatabaseManager()
        if db_manager.connect_to_database():
            summary = db_manager.get_transaction_summary()
            if not summary or summary.get('total_transactions', 0) == 0:
                steps_needed.append("Database Loading")
            db_manager.close_connection()
        else:
            steps_needed.append("Database Loading")
    except:
        steps_needed.append("Database Loading")
    
    if not os.path.exists('arima_model.pkl'):
        steps_needed.append("ARIMA Training")
    
    if not steps_needed:
        print("✅ All data processing steps are already completed!")
        print("\nYou can now run the services with: python run_all_services.py")
        return True
    
    print(f"📋 Steps needed: {', '.join(steps_needed)}")
    print()
    
    # Run processing steps
    success = True
    
    if "Data Cleaning" in steps_needed:
        if not run_data_cleaning():
            success = False
    
    if success and "JSON Transformation" in steps_needed:
        if not run_json_transformation():
            success = False
    
    if success and "Database Loading" in steps_needed:
        if not run_database_loading():
            success = False
    
    if success and "ARIMA Training" in steps_needed:
        if not train_arima_model():
            success = False
    
    # Final summary
    print("\n" + "="*70)
    if success:
        print(" 🎉 DATA PROCESSING COMPLETED SUCCESSFULLY!")
        print("="*70)
        print("\n✅ All data processing steps completed")
        print("\n🚀 Next steps:")
        print("   1. Run services: python run_all_services.py")
        print("   2. Access dashboard: http://localhost:3000")
        print("   3. Use API: http://localhost:8000")
    else:
        print(" ❌ DATA PROCESSING FAILED")
        print("="*70)
        print("\n⚠️ Some processing steps failed")
        print("Please check the error messages above")
    
    return success

if __name__ == "__main__":
    main()
