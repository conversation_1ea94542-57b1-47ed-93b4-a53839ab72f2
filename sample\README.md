# 📊 Transaction Data Samples

This directory contains 15 JSON files with randomly sampled transaction data from the cleaned transaction dataset.

## Overview

- **Total Files**: 15 (part1.json through part15.json)
- **Transactions per File**: 100 transactions each
- **Sampling Method**: Random sampling from different monthly periods
- **Source Data**: cleaned_transaction_data.csv (filtered to 2018-2019 data only)
- **Generated**: 2025-06-29 (parts 1-10), Extended with parts 11-15

## File Structure

Each JSON file contains:
- **metadata**: Information about the sample including part number, time period, sample size, and generation timestamp
- **transactions**: Array of 100 transaction records

## Time Periods Covered

### **Original Sample Parts (1-10)**
| File | Time Period | Month | Sample Size | Description |
|------|-------------|-------|-------------|-------------|
| part1.json | 2019-02-01 to 2019-02-20 | 2019-02 | 100 | Recent data, winter period |
| part2.json | 2019-01-04 to 2019-01-30 | 2019-01 | 100 | New year period, post-holiday |
| part3.json | 2018-09-01 to 2018-09-30 | 2018-09 | 100 | Back-to-school season |
| part4.json | 2018-05-01 to 2018-05-30 | 2018-05 | 100 | Spring period |
| part5.json | 2018-04-01 to 2018-04-30 | 2018-04 | 100 | Easter season |
| part6.json | 2018-07-01 to 2018-07-31 | 2018-07 | 100 | Summer period |
| part7.json | 2018-10-01 to 2018-10-31 | 2018-10 | 100 | Halloween/autumn |
| part8.json | 2018-03-02 to 2018-03-31 | 2018-03 | 100 | Early spring |
| part9.json | 2018-06-01 to 2018-06-30 | 2018-06 | 100 | Early summer |
| part10.json | 2018-12-01 to 2018-12-31 | 2018-12 | 100 | Holiday season |

### **Additional Sample Parts (11-15)**
| File | Time Period | Month | Sample Size | Description |
|------|-------------|-------|-------------|-------------|
| part11.json | 2018-11-01 to 2018-11-30 | 2018-11 | 100 | Black Friday / Pre-Christmas shopping |
| part12.json | 2018-08-01 to 2018-08-31 | 2018-08 | 100 | Late summer / Back-to-school period |
| part13.json | 2019-03-01 to 2019-03-31 | 2019-03 | 100 | Spring season / Easter preparations |
| part14.json | 2018-02-01 to 2018-02-28 | 2018-02 | 100 | Valentine's Day / Winter clearance |
| part15.json | 2018-01-01 to 2018-01-31 | 2018-01 | 100 | New Year / Post-holiday period |

## 🎯 Testing Benefits

The expanded sample set provides:
- ✅ **15 different time periods** for comprehensive testing
- ✅ **Seasonal diversity**: Holiday seasons, summer periods, spring/autumn
- ✅ **Shopping patterns**: Black Friday, Valentine's Day, Easter, Christmas
- ✅ **Year coverage**: Data spanning 2018-2019
- ✅ **Business cycles**: Post-holiday, back-to-school, seasonal clearances

## Transaction Fields

Each transaction record contains the following fields:
- `userid`: User ID (integer, null if -1 in original data)
- `transactionid`: Unique transaction identifier
- `transactiontime`: ISO format timestamp
- `itemcode`: Product code
- `itemdescription`: Product description
- `numberofitemspurchased`: Quantity purchased
- `costperitem`: Price per item
- `country`: Country of transaction
- `total_transaction_value`: Total transaction amount
- `transaction_year`: Year extracted from timestamp
- `transaction_month`: Month extracted from timestamp
- `transaction_day`: Day extracted from timestamp
- `transaction_weekday`: Day of week (0=Monday, 6=Sunday)
- `transaction_hour`: Hour extracted from timestamp

## Usage

These sample files can be used for:
- Testing data processing pipelines
- Machine learning model training/validation
- API testing and development
- Data analysis and visualization
- Performance testing with realistic data

## Data Quality Notes

- Original dataset contained some outlier dates (2028), which were filtered out
- Only transactions from 2018-2019 were included in the sampling
- Random sampling ensures diverse representation across different time periods
- Each sample maintains the original data structure and field types
