"""
ARIMA Forecasting Model for Financial Reports
This module implements ARIMA time series forecasting for transaction data
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import warnings
from database_manager import DatabaseManager
import pickle
import os

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Suppress warnings
warnings.filterwarnings('ignore')

try:
    from statsmodels.tsa.arima.model import ARIMA
    from statsmodels.tsa.stattools import adfuller
    from statsmodels.graphics.tsaplots import plot_acf, plot_pacf
    from statsmodels.stats.diagnostic import acorr_ljungbox
    STATSMODELS_AVAILABLE = True
except ImportError:
    logger.warning("statsmodels not available. Installing...")
    STATSMODELS_AVAILABLE = False

class ARIMAForecaster:
    """Class to handle ARIMA forecasting for financial transaction data"""
    
    def __init__(self):
        self.model = None
        self.fitted_model = None
        self.time_series_data = None
        self.db_manager = DatabaseManager()
        self.model_params = None
        self.forecast_results = None
    
    def fetch_time_series_data(self, period: str = 'daily', metric: str = 'revenue') -> pd.DataFrame:
        """Fetch time series data from database"""
        try:
            logger.info(f"Fetching {period} {metric} data from database...")
            
            if not self.db_manager.connect_to_database():
                raise Exception("Failed to connect to database")
            
            if period == 'daily':
                query = f"""
                    SELECT 
                        DATE(transactiontime) as date_value,
                        SUM(total_transaction_value) as revenue,
                        COUNT(*) as transaction_count,
                        SUM(numberofitemspurchased) as quantity
                    FROM transactions 
                    GROUP BY DATE(transactiontime)
                    ORDER BY date_value
                """
            elif period == 'monthly':
                query = f"""
                    SELECT 
                        DATE_FORMAT(transactiontime, '%Y-%m-01') as date_value,
                        SUM(total_transaction_value) as revenue,
                        COUNT(*) as transaction_count,
                        SUM(numberofitemspurchased) as quantity
                    FROM transactions 
                    GROUP BY YEAR(transactiontime), MONTH(transactiontime)
                    ORDER BY date_value
                """
            else:
                raise ValueError("Period must be 'daily' or 'monthly'")
            
            df = self.db_manager.query_database(query)
            
            if df.empty:
                raise Exception("No time series data found in database")
            
            # Convert date_value to datetime and set as index
            df['date_value'] = pd.to_datetime(df['date_value'])
            df.set_index('date_value', inplace=True)
            
            # Select the metric column
            if metric not in df.columns:
                raise ValueError(f"Metric '{metric}' not found in data")
            
            self.time_series_data = df[metric].sort_index()
            
            logger.info(f"Fetched {len(self.time_series_data)} {period} data points")
            logger.info(f"Date range: {self.time_series_data.index.min()} to {self.time_series_data.index.max()}")
            
            return df
            
        except Exception as e:
            logger.error(f"Error fetching time series data: {e}")
            raise
        finally:
            self.db_manager.close_connection()
    
    def check_stationarity(self, series: pd.Series) -> dict:
        """Check if time series is stationary using Augmented Dickey-Fuller test"""
        try:
            logger.info("Checking stationarity of time series...")
            
            # Perform ADF test
            adf_result = adfuller(series.dropna())
            
            result = {
                'adf_statistic': adf_result[0],
                'p_value': adf_result[1],
                'critical_values': adf_result[4],
                'is_stationary': adf_result[1] < 0.05
            }
            
            logger.info(f"ADF Statistic: {result['adf_statistic']:.6f}")
            logger.info(f"p-value: {result['p_value']:.6f}")
            logger.info(f"Is stationary: {result['is_stationary']}")
            
            return result
            
        except Exception as e:
            logger.error(f"Error checking stationarity: {e}")
            return {}
    
    def make_stationary(self, series: pd.Series, max_diff: int = 2) -> tuple:
        """Make time series stationary through differencing"""
        try:
            logger.info("Making time series stationary...")
            
            original_series = series.copy()
            current_series = series.copy()
            diff_order = 0
            
            for d in range(max_diff + 1):
                stationarity = self.check_stationarity(current_series)
                
                if stationarity.get('is_stationary', False):
                    logger.info(f"Series is stationary with {d} differences")
                    return current_series, d
                
                if d < max_diff:
                    current_series = current_series.diff().dropna()
                    diff_order = d + 1
            
            logger.warning(f"Series may not be fully stationary after {max_diff} differences")
            return current_series, diff_order
            
        except Exception as e:
            logger.error(f"Error making series stationary: {e}")
            return series, 0
    
    def find_optimal_parameters(self, series: pd.Series, max_p: int = 3, max_q: int = 3) -> tuple:
        """Find optimal ARIMA parameters using AIC criterion"""
        try:
            logger.info("Finding optimal ARIMA parameters...")
            
            # Make series stationary
            stationary_series, d = self.make_stationary(series)
            
            best_aic = float('inf')
            best_params = (0, d, 0)
            
            for p in range(max_p + 1):
                for q in range(max_q + 1):
                    try:
                        model = ARIMA(series, order=(p, d, q))
                        fitted_model = model.fit()
                        
                        if fitted_model.aic < best_aic:
                            best_aic = fitted_model.aic
                            best_params = (p, d, q)
                            
                    except Exception as e:
                        continue
            
            logger.info(f"Optimal ARIMA parameters: {best_params} (AIC: {best_aic:.2f})")
            return best_params
            
        except Exception as e:
            logger.error(f"Error finding optimal parameters: {e}")
            return (1, 1, 1)  # Default parameters
    
    def train_model(self, order: tuple = None) -> bool:
        """Train ARIMA model on time series data"""
        try:
            if self.time_series_data is None:
                raise Exception("No time series data available. Fetch data first.")
            
            logger.info("Training ARIMA model...")
            
            # Find optimal parameters if not provided
            if order is None:
                order = self.find_optimal_parameters(self.time_series_data)
            
            self.model_params = order
            
            # Train the model
            self.model = ARIMA(self.time_series_data, order=order)
            self.fitted_model = self.model.fit()
            
            logger.info(f"Model trained successfully with order {order}")
            logger.info(f"AIC: {self.fitted_model.aic:.2f}")
            logger.info(f"BIC: {self.fitted_model.bic:.2f}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error training model: {e}")
            return False
    
    def test_model(self, test_size: float = 0.2) -> dict:
        """Test model performance on held-out data"""
        try:
            if self.fitted_model is None:
                raise Exception("Model not trained. Train model first.")
            
            logger.info("Testing model performance...")
            
            # Split data
            split_point = int(len(self.time_series_data) * (1 - test_size))
            train_data = self.time_series_data[:split_point]
            test_data = self.time_series_data[split_point:]
            
            # Retrain on training data only
            model = ARIMA(train_data, order=self.model_params)
            fitted_model = model.fit()
            
            # Make predictions
            forecast = fitted_model.forecast(steps=len(test_data))
            
            # Calculate metrics
            mae = np.mean(np.abs(test_data - forecast))
            mse = np.mean((test_data - forecast) ** 2)
            rmse = np.sqrt(mse)
            mape = np.mean(np.abs((test_data - forecast) / test_data)) * 100
            
            results = {
                'mae': mae,
                'mse': mse,
                'rmse': rmse,
                'mape': mape,
                'test_size': len(test_data),
                'train_size': len(train_data)
            }
            
            logger.info(f"Model test results:")
            logger.info(f"  MAE: {mae:.2f}")
            logger.info(f"  RMSE: {rmse:.2f}")
            logger.info(f"  MAPE: {mape:.2f}%")
            
            return results
            
        except Exception as e:
            logger.error(f"Error testing model: {e}")
            return {}
    
    def forecast(self, steps: int = 30, confidence_interval: float = 0.95) -> dict:
        """Generate forecasts for future periods"""
        try:
            if self.fitted_model is None:
                raise Exception("Model not trained. Train model first.")
            
            logger.info(f"Generating {steps} step forecast...")
            
            # Generate forecast
            forecast_result = self.fitted_model.get_forecast(steps=steps)
            forecast_values = forecast_result.predicted_mean
            confidence_intervals = forecast_result.conf_int()
            
            # Create future dates
            last_date = self.time_series_data.index[-1]
            future_dates = pd.date_range(start=last_date + timedelta(days=1), periods=steps, freq='D')
            
            # Prepare results
            forecast_df = pd.DataFrame({
                'date': future_dates,
                'forecast': forecast_values.values,
                'lower_ci': confidence_intervals.iloc[:, 0].values,
                'upper_ci': confidence_intervals.iloc[:, 1].values
            })
            
            self.forecast_results = {
                'forecast_df': forecast_df,
                'forecast_values': forecast_values,
                'confidence_intervals': confidence_intervals,
                'steps': steps,
                'confidence_level': confidence_interval
            }
            
            logger.info(f"Forecast generated successfully")
            logger.info(f"Forecast range: {future_dates[0]} to {future_dates[-1]}")
            
            return self.forecast_results
            
        except Exception as e:
            logger.error(f"Error generating forecast: {e}")
            return {}
    
    def save_model(self, filepath: str = 'arima_model.pkl') -> bool:
        """Save trained model to file"""
        try:
            if self.fitted_model is None:
                raise Exception("No trained model to save")
            
            model_data = {
                'fitted_model': self.fitted_model,
                'model_params': self.model_params,
                'time_series_data': self.time_series_data,
                'forecast_results': self.forecast_results
            }
            
            with open(filepath, 'wb') as f:
                pickle.dump(model_data, f)
            
            logger.info(f"Model saved to {filepath}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving model: {e}")
            return False
    
    def load_model(self, filepath: str = 'arima_model.pkl') -> bool:
        """Load trained model from file"""
        try:
            if not os.path.exists(filepath):
                raise Exception(f"Model file {filepath} not found")
            
            with open(filepath, 'rb') as f:
                model_data = pickle.load(f)
            
            self.fitted_model = model_data['fitted_model']
            self.model_params = model_data['model_params']
            self.time_series_data = model_data['time_series_data']
            self.forecast_results = model_data.get('forecast_results')
            
            logger.info(f"Model loaded from {filepath}")
            return True
            
        except Exception as e:
            logger.error(f"Error loading model: {e}")
            return False

def install_statsmodels():
    """Install statsmodels if not available"""
    try:
        import subprocess
        import sys
        
        logger.info("Installing statsmodels...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "statsmodels"])
        logger.info("statsmodels installed successfully")
        return True
        
    except Exception as e:
        logger.error(f"Error installing statsmodels: {e}")
        return False

def main():
    """Main function to demonstrate ARIMA forecasting"""
    
    print("="*60)
    print("ARIMA FORECASTING MODEL")
    print("="*60)
    
    # Check if statsmodels is available
    if not STATSMODELS_AVAILABLE:
        print("statsmodels is required for ARIMA modeling")
        response = input("Install statsmodels? (y/n): ")
        if response.lower() in ['y', 'yes']:
            if install_statsmodels():
                print("Please restart the script after installation")
            return
        else:
            print("Cannot proceed without statsmodels")
            return
    
    try:
        # Initialize forecaster
        forecaster = ARIMAForecaster()
        
        # Fetch time series data
        print("\n1. Fetching time series data...")
        df = forecaster.fetch_time_series_data(period='daily', metric='revenue')
        print(f"✅ Fetched {len(df)} daily revenue data points")
        
        # Train model
        print("\n2. Training ARIMA model...")
        success = forecaster.train_model()
        if success:
            print(f"✅ Model trained with parameters {forecaster.model_params}")
        else:
            print("❌ Model training failed")
            return
        
        # Test model
        print("\n3. Testing model performance...")
        test_results = forecaster.test_model()
        if test_results:
            print(f"✅ Model tested - RMSE: {test_results['rmse']:.2f}, MAPE: {test_results['mape']:.2f}%")
        
        # Generate forecast
        print("\n4. Generating 30-day forecast...")
        forecast_results = forecaster.forecast(steps=30)
        if forecast_results:
            forecast_df = forecast_results['forecast_df']
            print(f"✅ Forecast generated for {len(forecast_df)} days")
            print("\nSample forecast:")
            print(forecast_df.head().to_string(index=False))
        
        # Save model
        print("\n5. Saving model...")
        if forecaster.save_model():
            print("✅ Model saved successfully")
        
        print("\n🎉 ARIMA forecasting pipeline completed!")
        
    except Exception as e:
        logger.error(f"Error in main execution: {e}")
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
