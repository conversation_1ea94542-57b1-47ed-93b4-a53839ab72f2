"""
Financial Reports ML - Complete Service Runner
This script manages all services for the Financial Reports ML system
"""

import subprocess
import time
import sys
import os
import signal
import threading
import requests
import webbrowser
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ServiceManager:
    """Manages all services for the Financial Reports ML system"""

    def __init__(self):
        self.api_process = None
        self.web_process = None
        self.running = False
        self.services_started = []

    def check_prerequisites(self):
        """Check if all required files exist"""
        logger.info("Checking prerequisites...")

        required_files = [
            "arima_api.py",
            "arima_forecasting.py",
            "database_manager.py",
            "data_cleaning.py",
            "simple_json_transformer.py",
            "requirements.txt",
            "app/index.html",
            "app/styles.css",
            "app/app.js",
            "app/server.py"
        ]

        missing_files = []
        for file in required_files:
            if not Path(file).exists():
                missing_files.append(file)

        if missing_files:
            logger.error(f"Missing required files: {missing_files}")
            return False

        logger.info("✅ All required files found")
        return True

    def check_data_files(self):
        """Check if data files exist"""
        logger.info("Checking data files...")

        data_files = {
            "transaction_data.csv": "Original transaction data",
            "cleaned_transaction_data.csv": "Cleaned transaction data",
            "transaction_records.json": "JSON transaction records",
            "transaction_aggregated.json": "Aggregated analytics data",
            "transaction_timeseries.json": "Time series data for ARIMA"
        }

        existing_files = []
        for file, description in data_files.items():
            if Path(file).exists():
                size = Path(file).stat().st_size / 1024  # KB
                logger.info(f"✅ {file} ({size:.1f} KB) - {description}")
                existing_files.append(file)
            else:
                logger.warning(f"⚠️ {file} - {description} (missing)")

        if not existing_files:
            logger.warning("No data files found. You may need to run data processing first.")
            return False

        return True

    def check_database_connection(self):
        """Check database connectivity"""
        logger.info("Checking database connection...")

        try:
            from database_manager import DatabaseManager
            db_manager = DatabaseManager()

            if db_manager.connect_to_database():
                summary = db_manager.get_transaction_summary()
                if summary and summary.get('total_transactions', 0) > 0:
                    logger.info(f"✅ Database connected with {summary['total_transactions']} transactions")
                    db_manager.close_connection()
                    return True
                else:
                    logger.warning("⚠️ Database connected but no data found")
                    db_manager.close_connection()
                    return False
            else:
                logger.warning("⚠️ Database connection failed")
                return False

        except Exception as e:
            logger.warning(f"⚠️ Database check failed: {e}")
            return False

    def start_api_service(self):
        """Start the FastAPI service"""
        logger.info("🚀 Starting API service...")

        try:
            self.api_process = subprocess.Popen(
                [sys.executable, "arima_api.py"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )

            # Wait for API to start
            time.sleep(3)

            # Check if API is responding
            for attempt in range(30):
                try:
                    response = requests.get("http://localhost:8001/health", timeout=2)
                    if response.status_code == 200:
                        logger.info("✅ API service started successfully")
                        self.services_started.append("API")
                        return True
                except requests.exceptions.RequestException:
                    if attempt < 29:
                        time.sleep(1)

            logger.error("❌ API service failed to start properly")
            return False

        except Exception as e:
            logger.error(f"❌ Error starting API service: {e}")
            return False

    def start_web_service(self):
        """Start the web dashboard service"""
        logger.info("🌐 Starting web dashboard service...")

        try:
            app_dir = Path("app")
            if not app_dir.exists():
                logger.error("❌ App directory not found!")
                return False

            # Start web server
            self.web_process = subprocess.Popen(
                [sys.executable, "server.py"],
                cwd=app_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                stdin=subprocess.PIPE
            )

            # Send default inputs (port 3000, no auto-open)
            try:
                self.web_process.stdin.write(b"3000\nn\n")
                self.web_process.stdin.flush()
            except:
                pass

            # Wait for web server to start
            time.sleep(2)

            # Check if web server is responding
            for attempt in range(10):
                try:
                    response = requests.get("http://localhost:3000", timeout=2)
                    if response.status_code == 200:
                        logger.info("✅ Web dashboard service started successfully")
                        self.services_started.append("Web Dashboard")
                        return True
                except requests.exceptions.RequestException:
                    if attempt < 9:
                        time.sleep(1)

            logger.info("✅ Web dashboard service started (may take a moment to respond)")
            self.services_started.append("Web Dashboard")
            return True

        except Exception as e:
            logger.error(f"❌ Error starting web service: {e}")
            return False

    def show_service_info(self):
        """Display service information"""
        print("\n" + "="*70)
        print(" 🎉 FINANCIAL REPORTS ML SERVICES RUNNING")
        print("="*70)

        print("\n🔗 Service URLs:")
        if "API" in self.services_started:
            print("   📡 API Service: http://localhost:8001")
            print("   📚 API Documentation: http://localhost:8001/docs")
            print("   🔍 API Health: http://localhost:8001/health")

        if "Web Dashboard" in self.services_started:
            print("   📊 Web Dashboard: http://localhost:3000")

        print("\n🎯 Available Features:")
        print("   • Real-time financial analytics")
        print("   • ARIMA forecasting models")
        print("   • Interactive data visualizations")
        print("   • Database integration")
        print("   • RESTful API endpoints")

        print("\n📋 API Endpoints:")
        print("   • GET  /health - Service health check")
        print("   • GET  /database-stats - Database statistics")
        print("   • GET  /model/info - ARIMA model information")
        print("   • GET  /forecast/quick - Quick forecast generation")
        print("   • POST /forecast - Custom forecast parameters")
        print("   • POST /train - Train ARIMA model")

        print("\n💡 Usage Tips:")
        print("   • Use the web dashboard for interactive analysis")
        print("   • Access API docs for programmatic integration")
        print("   • Train models before generating forecasts")
        print("   • Monitor service health regularly")

        print("\n🛑 To stop services: Press Ctrl+C")
        print("="*70)

    def monitor_services(self):
        """Monitor running services"""
        while self.running:
            try:
                # Check API process
                if self.api_process and self.api_process.poll() is not None:
                    logger.error("⚠️ API service stopped unexpectedly")
                    break

                # Check web process
                if self.web_process and self.web_process.poll() is not None:
                    logger.warning("⚠️ Web service stopped unexpectedly")
                    # Web service stopping is less critical, continue monitoring

                time.sleep(5)  # Check every 5 seconds

            except KeyboardInterrupt:
                break

    def stop_services(self):
        """Stop all running services"""
        logger.info("🛑 Stopping all services...")
        self.running = False

        if self.api_process:
            try:
                self.api_process.terminate()
                self.api_process.wait(timeout=10)
                logger.info("✅ API service stopped")
            except subprocess.TimeoutExpired:
                self.api_process.kill()
                logger.info("🔨 API service force-killed")
            except Exception as e:
                logger.warning(f"⚠️ Error stopping API service: {e}")

        if self.web_process:
            try:
                self.web_process.terminate()
                self.web_process.wait(timeout=10)
                logger.info("✅ Web service stopped")
            except subprocess.TimeoutExpired:
                self.web_process.kill()
                logger.info("🔨 Web service force-killed")
            except Exception as e:
                logger.warning(f"⚠️ Error stopping web service: {e}")

    def run_all_services(self):
        """Run all services for the Financial Reports ML system"""
        print("="*70)
        print(" FINANCIAL REPORTS ML - SERVICE MANAGER")
        print("="*70)
        print("Starting all services for the Financial Reports ML system...")
        print()

        try:
            # Check prerequisites
            if not self.check_prerequisites():
                logger.error("❌ Prerequisites check failed")
                return False

            # Check data files
            data_available = self.check_data_files()

            # Check database
            db_available = self.check_database_connection()

            if not data_available and not db_available:
                logger.warning("⚠️ No data found. You may need to run data processing first.")
                response = input("\nContinue anyway? (y/n): ").strip().lower()
                if response != 'y':
                    return False

            # Start API service
            if not self.start_api_service():
                logger.error("❌ Failed to start API service")
                return False

            # Start web service
            if not self.start_web_service():
                logger.warning("⚠️ Web service failed to start, but API is running")

            # Show service information
            self.show_service_info()

            # Ask about opening browser
            if "Web Dashboard" in self.services_started:
                response = input("\nOpen web dashboard in browser? (y/n): ").strip().lower()
                if response == 'y':
                    webbrowser.open("http://localhost:3000")
                    logger.info("🚀 Web dashboard opened in browser")

            # Start monitoring
            self.running = True
            monitor_thread = threading.Thread(target=self.monitor_services)
            monitor_thread.daemon = True
            monitor_thread.start()

            # Wait for user interrupt
            try:
                print("\n🔄 Services are running. Press Ctrl+C to stop all services...")
                while self.running:
                    time.sleep(1)
            except KeyboardInterrupt:
                pass

            return True

        except Exception as e:
            logger.error(f"❌ Unexpected error: {e}")
            return False
        finally:
            self.stop_services()

def main():
    """Main function"""
    service_manager = ServiceManager()

    # Handle Ctrl+C gracefully
    def signal_handler(sig, frame):
        service_manager.stop_services()
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)

    # Run all services
    success = service_manager.run_all_services()

    if success:
        print("\n✅ Service session completed successfully")
    else:
        print("\n❌ Service startup failed")
        sys.exit(1)

if __name__ == "__main__":
    main()
