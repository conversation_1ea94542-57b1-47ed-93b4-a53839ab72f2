"""
Database Manager for Financial Reports ML Project
This module handles database operations for loading JSON data and querying MySQL database
"""

import pandas as pd
import json
import logging
from sqlalchemy import create_engine, text, MetaData, Table, Column, Integer, String, Float, DateTime, Text
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base
import pymysql
from datetime import datetime, timedelta
import random
import numpy as np

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '',
    'database': 'financereports',
    'charset': 'utf8mb4'
}

Base = declarative_base()

class DatabaseManager:
    """Class to handle database operations for financial reports"""
    
    def __init__(self):
        self.engine = None
        self.session = None
        self.connection_string = f"mysql+pymysql://{DB_CONFIG['user']}:{DB_CONFIG['password']}@{DB_CONFIG['host']}/{DB_CONFIG['database']}"
    
    def connect_to_database(self):
        """Establish connection to MySQL database"""
        try:
            logger.info("Connecting to MySQL database...")
            self.engine = create_engine(self.connection_string, echo=False)
            
            # Test connection
            with self.engine.connect() as conn:
                result = conn.execute(text("SELECT 1"))
                logger.info("Database connection successful!")
            
            # Create session
            Session = sessionmaker(bind=self.engine)
            self.session = Session()
            
            return True
            
        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            logger.info("Please ensure XAMPP MySQL is running and database 'financereports' exists")
            return False
    
    def create_database_if_not_exists(self):
        """Create database if it doesn't exist"""
        try:
            # Connect without specifying database
            temp_connection_string = f"mysql+pymysql://{DB_CONFIG['user']}:{DB_CONFIG['password']}@{DB_CONFIG['host']}"
            temp_engine = create_engine(temp_connection_string)
            
            with temp_engine.connect() as conn:
                # Check if database exists
                result = conn.execute(text(f"SHOW DATABASES LIKE '{DB_CONFIG['database']}'"))
                if not result.fetchone():
                    # Create database
                    conn.execute(text(f"CREATE DATABASE {DB_CONFIG['database']}"))
                    logger.info(f"Database '{DB_CONFIG['database']}' created successfully")
                else:
                    logger.info(f"Database '{DB_CONFIG['database']}' already exists")
            
            temp_engine.dispose()
            return True
            
        except Exception as e:
            logger.error(f"Error creating database: {e}")
            return False
    
    def create_tables(self):
        """Create necessary tables for financial reports"""
        try:
            logger.info("Creating database tables...")
            
            # Create transactions table
            create_transactions_table = text("""
                CREATE TABLE IF NOT EXISTS transactions (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    userid INT NOT NULL,
                    transactionid BIGINT NOT NULL,
                    transactiontime DATETIME NOT NULL,
                    itemcode INT NOT NULL,
                    itemdescription TEXT,
                    numberofitemspurchased INT NOT NULL,
                    costperitem DECIMAL(10,2) NOT NULL,
                    country VARCHAR(100),
                    total_transaction_value DECIMAL(12,2),
                    transaction_year INT,
                    transaction_month INT,
                    transaction_day INT,
                    transaction_weekday INT,
                    transaction_hour INT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_userid (userid),
                    INDEX idx_transactiontime (transactiontime),
                    INDEX idx_country (country),
                    UNIQUE KEY unique_transaction (userid, transactionid, transactiontime)
                )
            """)
            
            # Create aggregated_data table for quick analytics
            create_aggregated_table = text("""
                CREATE TABLE IF NOT EXISTS aggregated_data (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    aggregation_type VARCHAR(50) NOT NULL,
                    aggregation_key VARCHAR(100) NOT NULL,
                    total_transactions INT,
                    unique_users INT,
                    total_revenue DECIMAL(15,2),
                    average_transaction_value DECIMAL(10,2),
                    total_quantity INT,
                    aggregation_date DATE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_type_key (aggregation_type, aggregation_key),
                    INDEX idx_date (aggregation_date)
                )
            """)
            
            # Create time_series table for ARIMA data
            create_timeseries_table = text("""
                CREATE TABLE IF NOT EXISTS time_series (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    date_value DATE NOT NULL,
                    period_type VARCHAR(20) NOT NULL,
                    revenue DECIMAL(12,2),
                    transaction_count INT,
                    quantity INT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE KEY unique_date_period (date_value, period_type),
                    INDEX idx_date (date_value),
                    INDEX idx_period (period_type)
                )
            """)
            
            with self.engine.connect() as conn:
                conn.execute(create_transactions_table)
                conn.execute(create_aggregated_table)
                conn.execute(create_timeseries_table)
                conn.commit()
            
            logger.info("Database tables created successfully!")
            return True
            
        except Exception as e:
            logger.error(f"Error creating tables: {e}")
            return False
    
    def generate_random_data(self, num_records: int = 1000) -> pd.DataFrame:
        """Generate random financial transaction data for testing"""
        logger.info(f"Generating {num_records} random transaction records...")
        
        # Sample data for generation
        countries = ['United Kingdom', 'France', 'Germany', 'Spain', 'Italy', 'Netherlands', 
                    'Belgium', 'Switzerland', 'Austria', 'Portugal']
        
        products = [
            'COFFEE MUG', 'PICTURE FRAME', 'CANDLE HOLDER', 'DECORATIVE PLATE',
            'KITCHEN TOWEL', 'STORAGE BOX', 'WALL CLOCK', 'CUSHION COVER',
            'LAMP SHADE', 'FLOWER POT', 'NOTEBOOK', 'PEN SET', 'BOOKMARK',
            'KEYCHAIN', 'COASTER SET', 'WINE GLASS', 'DINNER PLATE', 'BOWL SET'
        ]
        
        random_data = []
        
        for i in range(num_records):
            # Generate random transaction
            userid = random.randint(100000, 999999)
            transactionid = random.randint(6000000, 9999999)
            
            # Random date within last 2 years
            start_date = datetime.now() - timedelta(days=730)
            random_days = random.randint(0, 730)
            transaction_time = start_date + timedelta(days=random_days)
            
            itemcode = random.randint(400000, 999999)
            itemdescription = random.choice(products)
            quantity = random.randint(1, 50)
            cost_per_item = round(random.uniform(0.5, 50.0), 2)
            country = random.choice(countries)
            
            total_value = round(quantity * cost_per_item, 2)
            
            random_data.append({
                'userid': userid,
                'transactionid': transactionid,
                'transactiontime': transaction_time,
                'itemcode': itemcode,
                'itemdescription': itemdescription,
                'numberofitemspurchased': quantity,
                'costperitem': cost_per_item,
                'country': country,
                'total_transaction_value': total_value,
                'transaction_year': transaction_time.year,
                'transaction_month': transaction_time.month,
                'transaction_day': transaction_time.day,
                'transaction_weekday': transaction_time.weekday(),
                'transaction_hour': transaction_time.hour
            })
        
        df = pd.DataFrame(random_data)
        logger.info(f"Generated {len(df)} random records")
        return df
    
    def load_json_to_database(self, json_file_path: str, table_name: str = 'transactions'):
        """Load JSON transaction data to database"""
        try:
            logger.info(f"Loading JSON data from {json_file_path} to {table_name} table...")
            
            # Load JSON data
            with open(json_file_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)
            
            # Extract transactions from JSON structure
            if 'transactions' in json_data:
                transactions = json_data['transactions']
            else:
                transactions = json_data
            
            # Convert to DataFrame
            df = pd.DataFrame(transactions)
            
            # Convert transactiontime to datetime
            if 'transactiontime' in df.columns:
                df['transactiontime'] = pd.to_datetime(df['transactiontime'])
            
            # Load to database
            df.to_sql(table_name, self.engine, if_exists='append', index=False, method='multi')
            
            logger.info(f"Successfully loaded {len(df)} records to {table_name} table")
            return True
            
        except Exception as e:
            logger.error(f"Error loading JSON to database: {e}")
            return False
    
    def query_database(self, query: str) -> pd.DataFrame:
        """Execute query and return results as DataFrame"""
        try:
            logger.info(f"Executing query: {query[:100]}...")
            result_df = pd.read_sql(query, self.engine)
            logger.info(f"Query returned {len(result_df)} rows")
            return result_df
            
        except Exception as e:
            logger.error(f"Error executing query: {e}")
            return pd.DataFrame()
    
    def get_transaction_summary(self) -> dict:
        """Get summary statistics from transactions table"""
        try:
            summary_query = """
                SELECT 
                    COUNT(*) as total_transactions,
                    COUNT(DISTINCT userid) as unique_users,
                    COUNT(DISTINCT country) as unique_countries,
                    SUM(total_transaction_value) as total_revenue,
                    AVG(total_transaction_value) as avg_transaction_value,
                    MIN(transactiontime) as earliest_transaction,
                    MAX(transactiontime) as latest_transaction
                FROM transactions
            """
            
            result = self.query_database(summary_query)
            if not result.empty:
                return result.iloc[0].to_dict()
            else:
                return {}
                
        except Exception as e:
            logger.error(f"Error getting transaction summary: {e}")
            return {}
    
    def get_time_series_data(self, period: str = 'daily') -> pd.DataFrame:
        """Get time series data for ARIMA modeling"""
        try:
            if period == 'daily':
                query = """
                    SELECT 
                        DATE(transactiontime) as date_value,
                        SUM(total_transaction_value) as revenue,
                        COUNT(*) as transaction_count,
                        SUM(numberofitemspurchased) as quantity
                    FROM transactions 
                    GROUP BY DATE(transactiontime)
                    ORDER BY date_value
                """
            elif period == 'monthly':
                query = """
                    SELECT 
                        DATE_FORMAT(transactiontime, '%Y-%m-01') as date_value,
                        SUM(total_transaction_value) as revenue,
                        COUNT(*) as transaction_count,
                        SUM(numberofitemspurchased) as quantity
                    FROM transactions 
                    GROUP BY YEAR(transactiontime), MONTH(transactiontime)
                    ORDER BY date_value
                """
            else:
                raise ValueError("Period must be 'daily' or 'monthly'")
            
            return self.query_database(query)
            
        except Exception as e:
            logger.error(f"Error getting time series data: {e}")
            return pd.DataFrame()
    
    def close_connection(self):
        """Close database connection"""
        if self.session:
            self.session.close()
        if self.engine:
            self.engine.dispose()
        logger.info("Database connection closed")

def main():
    """Main function to test database operations"""
    
    # Initialize database manager
    db_manager = DatabaseManager()
    
    try:
        # Create database if needed
        if not db_manager.create_database_if_not_exists():
            logger.error("Failed to create database")
            return
        
        # Connect to database
        if not db_manager.connect_to_database():
            logger.error("Failed to connect to database")
            return
        
        # Create tables
        if not db_manager.create_tables():
            logger.error("Failed to create tables")
            return
        
        # Test with random data
        print("\n" + "="*60)
        print("DATABASE SETUP COMPLETED")
        print("="*60)
        print("✅ Database connection established")
        print("✅ Tables created successfully")
        print("\nReady to load JSON data!")
        
        # Optionally generate and load random data for testing
        response = input("\nDo you want to generate and load random test data? (y/n): ")
        if response.lower() in ['y', 'yes']:
            random_df = db_manager.generate_random_data(100)
            random_df.to_sql('transactions', db_manager.engine, if_exists='append', index=False)
            print(f"✅ Loaded {len(random_df)} random test records")
        
        # Get summary
        summary = db_manager.get_transaction_summary()
        if summary:
            print(f"\nDatabase Summary:")
            for key, value in summary.items():
                print(f"  {key}: {value}")
        
    except Exception as e:
        logger.error(f"Error in main execution: {e}")
    
    finally:
        db_manager.close_connection()

if __name__ == "__main__":
    main()
