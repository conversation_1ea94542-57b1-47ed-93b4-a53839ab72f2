<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced ARIMA Forecasting Dashboard</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-chart-line"></i>
                    <h1>Enhanced ARIMA Forecasting</h1>
                </div>
                <div class="header-info">
                    <div class="status-indicator" id="apiStatus">
                        <i class="fas fa-circle"></i>
                        <span>Connecting...</span>
                    </div>
                    <div class="version">v2.0</div>
                </div>
            </div>
        </header>

        <!-- Navigation -->
        <nav class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('dashboard')">
                <i class="fas fa-tachometer-alt"></i> Dashboard
            </button>
            <button class="nav-tab" onclick="showTab('models')">
                <i class="fas fa-brain"></i> Models
            </button>
            <button class="nav-tab" onclick="showTab('forecasting')">
                <i class="fas fa-crystal-ball"></i> Forecasting
            </button>
            <button class="nav-tab" onclick="showTab('analytics')">
                <i class="fas fa-chart-bar"></i> Analytics
            </button>
            <button class="nav-tab" onclick="showTab('settings')">
                <i class="fas fa-cog"></i> Settings
            </button>
        </nav>

        <!-- Dashboard Tab -->
        <div id="dashboard" class="tab-content active">
            <div class="dashboard-grid">
                <!-- System Status Card -->
                <div class="card status-card">
                    <div class="card-header">
                        <h3><i class="fas fa-heartbeat"></i> System Status</h3>
                        <button class="refresh-btn" onclick="refreshSystemStatus()">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                    <div class="card-content">
                        <div class="status-grid">
                            <div class="status-item">
                                <div class="status-label">API Server</div>
                                <div class="status-value" id="apiServerStatus">
                                    <i class="fas fa-spinner fa-spin"></i> Checking...
                                </div>
                            </div>
                            <div class="status-item">
                                <div class="status-label">Models Trained</div>
                                <div class="status-value" id="modelsCount">
                                    <i class="fas fa-spinner fa-spin"></i> Loading...
                                </div>
                            </div>
                            <div class="status-item">
                                <div class="status-label">Ensemble Status</div>
                                <div class="status-value" id="ensembleStatus">
                                    <i class="fas fa-spinner fa-spin"></i> Loading...
                                </div>
                            </div>
                            <div class="status-item">
                                <div class="status-label">Last Training</div>
                                <div class="status-value" id="lastTraining">
                                    <i class="fas fa-spinner fa-spin"></i> Loading...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions Card -->
                <div class="card actions-card">
                    <div class="card-header">
                        <h3><i class="fas fa-bolt"></i> Quick Actions</h3>
                    </div>
                    <div class="card-content">
                        <div class="action-buttons">
                            <button class="action-btn primary" onclick="trainModels()">
                                <i class="fas fa-play"></i>
                                <span>Train Models</span>
                            </button>
                            <button class="action-btn secondary" onclick="quickForecast()">
                                <i class="fas fa-forward"></i>
                                <span>Quick Forecast</span>
                            </button>
                            <button class="action-btn tertiary" onclick="viewPerformance()">
                                <i class="fas fa-chart-line"></i>
                                <span>Performance</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Model Performance Card -->
                <div class="card performance-card">
                    <div class="card-header">
                        <h3><i class="fas fa-trophy"></i> Model Performance</h3>
                    </div>
                    <div class="card-content">
                        <div class="performance-metrics" id="performanceMetrics">
                            <div class="metric-item">
                                <div class="metric-label">Accuracy</div>
                                <div class="metric-value">--</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-label">MAPE</div>
                                <div class="metric-value">--</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-label">RMSE</div>
                                <div class="metric-value">--</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Forecasts Card -->
                <div class="card forecasts-card">
                    <div class="card-header">
                        <h3><i class="fas fa-history"></i> Recent Forecasts</h3>
                    </div>
                    <div class="card-content">
                        <div class="forecast-list" id="recentForecasts">
                            <div class="empty-state">
                                <i class="fas fa-chart-line"></i>
                                <p>No forecasts generated yet</p>
                                <button class="btn-link" onclick="showTab('forecasting')">Create Forecast</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Models Tab -->
        <div id="models" class="tab-content">
            <div class="models-grid">
                <!-- Model Training Card -->
                <div class="card training-card">
                    <div class="card-header">
                        <h3><i class="fas fa-brain"></i> Model Training</h3>
                    </div>
                    <div class="card-content">
                        <div class="training-options">
                            <div class="option-group">
                                <label>Training Mode</label>
                                <select id="trainingMode" class="form-select">
                                    <option value="ensemble">Ensemble Training (Recommended)</option>
                                    <option value="single">Single Model Training</option>
                                    <option value="auto">Auto-Optimization</option>
                                </select>
                            </div>

                            <div class="option-group">
                                <label>Data Metric</label>
                                <select id="dataMetric" class="form-select">
                                    <option value="revenue">Revenue</option>
                                    <option value="transaction_count">Transaction Count</option>
                                    <option value="quantity">Quantity</option>
                                </select>
                            </div>

                            <div class="option-group">
                                <label>Time Period</label>
                                <select id="timePeriod" class="form-select">
                                    <option value="daily">Daily</option>
                                    <option value="weekly">Weekly</option>
                                    <option value="monthly">Monthly</option>
                                </select>
                            </div>

                            <div class="training-controls">
                                <button class="btn primary" onclick="startTraining()" id="trainBtn">
                                    <i class="fas fa-play"></i> Start Training
                                </button>
                                <button class="btn secondary" onclick="stopTraining()" id="stopBtn" disabled>
                                    <i class="fas fa-stop"></i> Stop Training
                                </button>
                            </div>
                        </div>

                        <div class="training-progress" id="trainingProgress" style="display: none;">
                            <div class="progress-bar">
                                <div class="progress-fill" id="progressFill"></div>
                            </div>
                            <div class="progress-text" id="progressText">Initializing...</div>
                        </div>

                        <div class="training-results" id="trainingResults" style="display: none;">
                            <h4>Training Results</h4>
                            <div class="results-grid" id="resultsGrid">
                                <!-- Results will be populated here -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Model Status Card -->
                <div class="card model-status-card">
                    <div class="card-header">
                        <h3><i class="fas fa-list"></i> Model Status</h3>
                        <button class="refresh-btn" onclick="refreshModelStatus()">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                    <div class="card-content">
                        <div class="model-list" id="modelList">
                            <div class="loading-state">
                                <i class="fas fa-spinner fa-spin"></i>
                                <p>Loading model information...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Forecasting Tab -->
        <div id="forecasting" class="tab-content">
            <div class="forecasting-grid">
                <!-- Forecast Configuration Card -->
                <div class="card forecast-config-card">
                    <div class="card-header">
                        <h3><i class="fas fa-crystal-ball"></i> Forecast Configuration</h3>
                    </div>
                    <div class="card-content">
                        <div class="config-options">
                            <div class="option-group">
                                <label>Forecast Horizon</label>
                                <select id="forecastHorizon" class="form-select">
                                    <option value="7">7 Days</option>
                                    <option value="14">14 Days</option>
                                    <option value="30" selected>30 Days</option>
                                    <option value="60">60 Days</option>
                                    <option value="90">90 Days</option>
                                    <option value="365">1 Year</option>
                                </select>
                            </div>

                            <div class="option-group">
                                <label>Confidence Level</label>
                                <select id="confidenceLevel" class="form-select">
                                    <option value="0.80">80%</option>
                                    <option value="0.90">90%</option>
                                    <option value="0.95" selected>95%</option>
                                    <option value="0.99">99%</option>
                                </select>
                            </div>

                            <div class="option-group">
                                <label>Forecast Type</label>
                                <select id="forecastType" class="form-select">
                                    <option value="revenue">Revenue Forecast</option>
                                    <option value="transaction_count">Transaction Count</option>
                                    <option value="quantity">Quantity Forecast</option>
                                </select>
                            </div>

                            <div class="option-group">
                                <label>Sample Data (Optional)</label>
                                <select id="sampleData" class="form-select">
                                    <option value="">Use Full Dataset</option>
                                    <option value="1">Part 1 - Feb 2019 (Winter)</option>
                                    <option value="2">Part 2 - Jan 2019 (Post-Holiday)</option>
                                    <option value="3">Part 3 - Sep 2018 (Back-to-School)</option>
                                    <option value="4">Part 4 - May 2018 (Spring)</option>
                                    <option value="5">Part 5 - Apr 2018 (Easter)</option>
                                    <option value="6">Part 6 - Jul 2018 (Summer)</option>
                                    <option value="7">Part 7 - Oct 2018 (Halloween)</option>
                                    <option value="8">Part 8 - Mar 2018 (Early Spring)</option>
                                    <option value="9">Part 9 - Jun 2018 (Early Summer)</option>
                                    <option value="10">Part 10 - Dec 2018 (Holiday Season)</option>
                                    <option value="11">Part 11 - Nov 2018 (Black Friday)</option>
                                    <option value="12">Part 12 - Aug 2018 (Late Summer)</option>
                                    <option value="13">Part 13 - Mar 2019 (Spring)</option>
                                    <option value="14">Part 14 - Feb 2018 (Valentine's Day)</option>
                                    <option value="15">Part 15 - Jan 2018 (New Year)</option>
                                </select>
                            </div>

                            <div class="forecast-controls">
                                <button class="btn primary" onclick="generateForecast()" id="forecastBtn">
                                    <i class="fas fa-magic"></i> Generate Forecast
                                </button>
                                <button class="btn secondary" onclick="clearForecast()">
                                    <i class="fas fa-trash"></i> Clear
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Forecast Results Card -->
                <div class="card forecast-results-card">
                    <div class="card-header">
                        <h3><i class="fas fa-chart-area"></i> Forecast Results</h3>
                        <div class="chart-controls">
                            <button class="chart-btn" onclick="downloadChart()">
                                <i class="fas fa-download"></i>
                            </button>
                            <button class="chart-btn" onclick="fullscreenChart()">
                                <i class="fas fa-expand"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="chart-container">
                            <canvas id="forecastChart"></canvas>
                        </div>
                        <div class="forecast-summary" id="forecastSummary" style="display: none;">
                            <div class="summary-grid">
                                <div class="summary-item">
                                    <div class="summary-label">Forecast Period</div>
                                    <div class="summary-value" id="forecastPeriodValue">--</div>
                                </div>
                                <div class="summary-item">
                                    <div class="summary-label">Average Prediction</div>
                                    <div class="summary-value" id="avgPredictionValue">--</div>
                                </div>
                                <div class="summary-item">
                                    <div class="summary-label">Confidence Range</div>
                                    <div class="summary-value" id="confidenceRangeValue">--</div>
                                </div>
                                <div class="summary-item">
                                    <div class="summary-label">Model Accuracy</div>
                                    <div class="summary-value" id="modelAccuracyValue">--</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Analytics Tab -->
        <div id="analytics" class="tab-content">
            <div class="analytics-grid">
                <!-- Performance Analytics Card -->
                <div class="card analytics-card">
                    <div class="card-header">
                        <h3><i class="fas fa-chart-bar"></i> Performance Analytics</h3>
                    </div>
                    <div class="card-content">
                        <div class="analytics-tabs">
                            <button class="analytics-tab active" onclick="showAnalytics('accuracy')">Accuracy</button>
                            <button class="analytics-tab" onclick="showAnalytics('speed')">Speed</button>
                            <button class="analytics-tab" onclick="showAnalytics('comparison')">Comparison</button>
                        </div>

                        <div id="accuracy-analytics" class="analytics-content active">
                            <canvas id="accuracyChart"></canvas>
                        </div>

                        <div id="speed-analytics" class="analytics-content">
                            <canvas id="speedChart"></canvas>
                        </div>

                        <div id="comparison-analytics" class="analytics-content">
                            <canvas id="comparisonChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Model Insights Card -->
                <div class="card insights-card">
                    <div class="card-header">
                        <h3><i class="fas fa-lightbulb"></i> Model Insights</h3>
                    </div>
                    <div class="card-content">
                        <div class="insights-list" id="modelInsights">
                            <div class="insight-item">
                                <div class="insight-icon">
                                    <i class="fas fa-info-circle"></i>
                                </div>
                                <div class="insight-content">
                                    <h4>Enhanced Performance</h4>
                                    <p>Ensemble methods provide 15-25% better accuracy than single models.</p>
                                </div>
                            </div>
                            <div class="insight-item">
                                <div class="insight-icon">
                                    <i class="fas fa-rocket"></i>
                                </div>
                                <div class="insight-content">
                                    <h4>Speed Optimization</h4>
                                    <p>Auto-ARIMA with caching delivers 5-10x faster parameter search.</p>
                                </div>
                            </div>
                            <div class="insight-item">
                                <div class="insight-icon">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <div class="insight-content">
                                    <h4>Production Ready</h4>
                                    <p>Robust error handling and validation for business-critical forecasting.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings Tab -->
        <div id="settings" class="tab-content">
            <div class="settings-grid">
                <!-- API Configuration Card -->
                <div class="card api-config-card">
                    <div class="card-header">
                        <h3><i class="fas fa-cog"></i> API Configuration</h3>
                    </div>
                    <div class="card-content">
                        <div class="config-options">
                            <div class="option-group">
                                <label>API Endpoint</label>
                                <input type="text" id="apiEndpoint" class="form-input" value="http://localhost:8001">
                            </div>

                            <div class="option-group">
                                <label>Request Timeout (seconds)</label>
                                <input type="number" id="requestTimeout" class="form-input" value="30" min="5" max="300">
                            </div>

                            <div class="option-group">
                                <label>Auto-refresh Interval (seconds)</label>
                                <input type="number" id="refreshInterval" class="form-input" value="30" min="10" max="300">
                            </div>

                            <div class="option-group">
                                <label>Chart Theme</label>
                                <select id="chartTheme" class="form-select">
                                    <option value="light">Light</option>
                                    <option value="dark">Dark</option>
                                    <option value="auto">Auto</option>
                                </select>
                            </div>

                            <div class="settings-controls">
                                <button class="btn primary" onclick="saveSettings()">
                                    <i class="fas fa-save"></i> Save Settings
                                </button>
                                <button class="btn secondary" onclick="resetSettings()">
                                    <i class="fas fa-undo"></i> Reset
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Information Card -->
                <div class="card system-info-card">
                    <div class="card-header">
                        <h3><i class="fas fa-info"></i> System Information</h3>
                    </div>
                    <div class="card-content">
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">Version</div>
                                <div class="info-value">Enhanced ARIMA v2.0</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">API Status</div>
                                <div class="info-value" id="settingsApiStatus">Connected</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Models Available</div>
                                <div class="info-value">5 Types</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Sample Data Parts</div>
                                <div class="info-value">15 Parts</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Features</div>
                                <div class="info-value">Ensemble, Auto-ARIMA, Caching</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading Overlay -->
        <div id="loadingOverlay" class="loading-overlay" style="display: none;">
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <div class="loading-text">Processing...</div>
            </div>
        </div>

        <!-- Notification Container -->
        <div id="notificationContainer" class="notification-container"></div>
    </div>

    <!-- Scripts -->
    <script src="app.js"></script>
</body>
</html>