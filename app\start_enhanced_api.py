#!/usr/bin/env python3
"""
Simple script to start the enhanced ARIMA API
"""

import sys
import os

# Add parent directory to Python path
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

print("🚀 Starting Enhanced ARIMA API...")
print(f"📁 Parent directory: {parent_dir}")

try:
    # Test import
    from standalone_enhanced_arima import StandaloneEnhancedARIMA
    print("✅ Enhanced ARIMA imported successfully")
    
    # Start the API
    import uvicorn
    from enhanced_arima_api import app
    
    print("📡 API will be available at: http://localhost:8001")
    print("📚 Documentation at: http://localhost:8001/docs")
    print("⌨️ Press Ctrl+C to stop the server")
    
    uvicorn.run(app, host="0.0.0.0", port=8001)
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("💡 Make sure standalone_enhanced_arima.py is in the parent directory")
    sys.exit(1)
except Exception as e:
    print(f"❌ Error starting API: {e}")
    sys.exit(1)
